#!/usr/bin/env python3
"""
OKX下单测试脚本
用于测试OKX永续合约下单功能
"""

import os
import ccxt
from dotenv import load_dotenv

def test_okx_order_parameters():
    """测试OKX下单参数"""
    print("🚀 OKX下单参数测试")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    if not all([api_key, secret_key, passphrase]):
        print("❌ 请确保在.env文件中配置了所有必需的API密钥")
        return False
    
    try:
        # 初始化OKX交易所
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': False,  # 使用生产环境（小心！）
            'options': {
                'defaultType': 'swap',  # 永续合约
                'adjustForTimeDifference': True,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'recvWindow': 10000
            }
        })
        
        print("✅ 交易所连接成功")
        
        # 测试交易对
        symbol = 'BTC-USDT-SWAP'
        
        # 1. 测试获取市场信息
        print(f"\n📊 测试市场信息获取...")
        try:
            ticker = exchange.fetch_ticker(symbol)
            current_price = ticker['last']
            print(f"✅ {symbol} 当前价格: ${current_price}")
        except Exception as e:
            print(f"❌ 获取市场信息失败: {str(e)}")
            return False
        
        # 2. 测试获取账户余额
        print(f"\n💰 测试账户余额获取...")
        try:
            balance = exchange.fetch_balance()
            usdt_balance = balance.get('USDT', {}).get('free', 0)
            print(f"✅ USDT余额: {usdt_balance}")
        except Exception as e:
            print(f"❌ 获取账户余额失败: {str(e)}")
            return False
        
        # 3. 测试设置杠杆
        print(f"\n⚖️ 测试杠杆设置...")
        try:
            leverage = 2
            leverage_params = {
                'marginMode': 'isolated',
                'posSide': 'net',  # OKX永续合约使用net模式
                'type': 'swap'
            }
            exchange.set_leverage(leverage, symbol, leverage_params)
            print(f"✅ 杠杆设置成功: {leverage}x")
        except Exception as e:
            print(f"❌ 杠杆设置失败: {str(e)}")
            print(f"   错误详情: {e}")
            # 杠杆设置失败不影响下单测试，继续执行
        
        # 4. 测试下单参数（不实际下单）
        print(f"\n📝 测试下单参数构造...")
        try:
            # 构造一个小额测试订单参数
            test_amount = 0.001  # 非常小的数量用于测试
            order_params = {
                'marginMode': 'isolated',  # 逐仓模式
                'posSide': 'net',  # OKX永续合约使用net模式
                'newClientOrderId': f'test_{int(time.time() * 1000)}'
            }
            
            print("✅ 下单参数构造成功:")
            print(f"   交易对: {symbol}")
            print(f"   数量: {test_amount}")
            print(f"   参数: {order_params}")
            
            # 注意：这里不实际下单，只是验证参数
            print("⚠️  注意: 为了安全，此脚本不会实际下单")
            print("   如需实际下单，请取消下面代码的注释并谨慎操作")
            
            # 实际下单代码（已注释）
            # order = exchange.create_market_buy_order(symbol, test_amount, None, order_params)
            # print(f"✅ 下单成功: {order}")
            
        except Exception as e:
            print(f"❌ 下单参数测试失败: {str(e)}")
            return False
        
        print(f"\n🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return False

def show_okx_order_guide():
    """显示OKX下单指南"""
    print(f"\n📚 OKX永续合约下单指南:")
    print("=" * 50)
    
    print("1. 正确的交易对格式:")
    print("   ✅ BTC-USDT-SWAP (永续合约)")
    print("   ❌ BTCUSDT (币安格式)")
    print("   ❌ BTC/USDT (现货格式)")
    
    print("\n2. 正确的下单参数:")
    print("   ✅ posSide: 'net' (单向持仓模式)")
    print("   ❌ positionSide: 'BOTH' (币安格式)")
    print("   ❌ posSide: 'long'/'short' (双向持仓模式)")
    
    print("\n3. 杠杆设置参数:")
    print("   ✅ marginMode: 'isolated' (逐仓)")
    print("   ✅ type: 'swap' (永续合约)")
    print("   ✅ posSide: 'net' (单向持仓)")
    
    print("\n4. 常见错误及解决方案:")
    print("   错误: Parameter posSide error")
    print("   解决: 使用 posSide: 'net' 而不是 'long'/'short'")
    print("   ")
    print("   错误: okx does not have market symbol ETHUSDT")
    print("   解决: 使用 ETH-USDT-SWAP 而不是 ETHUSDT")

def main():
    """主函数"""
    import time
    
    # 添加time模块到全局作用域
    globals()['time'] = time
    
    show_okx_order_guide()
    
    print(f"\n" + "=" * 50)
    choice = input("是否运行OKX下单参数测试？(y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        success = test_okx_order_parameters()
        
        if success:
            print(f"\n🎉 测试完成！现在可以安全地使用量化交易机器人了。")
        else:
            print(f"\n❌ 测试失败，请检查配置后重试。")
    else:
        print("测试已取消。")

if __name__ == "__main__":
    main()
