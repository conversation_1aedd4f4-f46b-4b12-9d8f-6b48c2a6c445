#!/usr/bin/env python3
"""
AI触发分析检查脚本
用于诊断AI触发分析功能是否正常工作
"""

import os
import json
import requests
import ccxt
from dotenv import load_dotenv
import time

def check_environment():
    """检查环境配置"""
    print("=== 环境配置检查 ===")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    okx_api_key = os.getenv('OKX_API_KEY')
    okx_secret_key = os.getenv('OKX_SECRET_KEY')
    okx_passphrase = os.getenv('OKX_PASSPHRASE')
    news_api_key = os.getenv('NEWS_API_KEY')
    deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')

    print(f"OKX API Key: {'✓ 已配置' if okx_api_key else '✗ 未配置'}")
    print(f"OKX Secret Key: {'✓ 已配置' if okx_secret_key else '✗ 未配置'}")
    print(f"OKX Passphrase: {'✓ 已配置' if okx_passphrase else '✗ 未配置'}")
    print(f"News API Key: {'✓ 已配置' if news_api_key else '✗ 未配置'}")
    print(f"DeepSeek API Key: {'✓ 已配置' if deepseek_api_key else '✗ 未配置'}")

    return {
        'okx_api_key': okx_api_key,
        'okx_secret_key': okx_secret_key,
        'okx_passphrase': okx_passphrase,
        'news_api_key': news_api_key,
        'deepseek_api_key': deepseek_api_key
    }

def check_okx_connection(api_key, secret_key, passphrase):
    """检查OKX连接"""
    print("\n=== OKX连接检查 ===")

    try:
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': False,
            'options': {
                'defaultType': 'swap',
                'adjustForTimeDifference': True,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'recvWindow': 10000
            }
        })

        # 测试连接
        ticker = exchange.fetch_ticker('BTC-USDT-SWAP')
        print(f"✓ OKX连接成功")
        print(f"  BTC-USDT-SWAP 当前价格: ${ticker['last']}")
        return True

    except Exception as e:
        print(f"✗ OKX连接失败: {str(e)}")
        return False

def check_news_api(api_key):
    """检查News API"""
    print("\n=== News API检查 ===")
    
    try:
        url = "https://newsapi.org/v2/everything"
        params = {
            'q': 'bitcoin OR cryptocurrency',
            'language': 'en',
            'sortBy': 'publishedAt',
            'pageSize': 5,
            'apiKey': api_key
        }
        
        response = requests.get(url, params=params, timeout=(10, 30))  # (连接超时, 读取超时)
        
        if response.status_code == 200:
            data = response.json()
            articles = data.get('articles', [])
            print(f"✓ News API连接成功")
            print(f"  获取到 {len(articles)} 条新闻")
            return True
        else:
            print(f"✗ News API连接失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ News API连接失败: {str(e)}")
        return False

def check_deepseek_api(api_key):
    """检查DeepSeek API"""
    print("\n=== DeepSeek AI API检查 ===")
    
    try:
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        api_url = "https://api.deepseek.com/v1/chat/completions"
        api_data = {
            "model": "deepseek-chat",
            "messages": [{"role": "user", "content": "Hello, test connection"}],
            "temperature": 0.7,
            "max_tokens": 10
        }
        
        response = requests.post(api_url, json=api_data, headers=headers, timeout=30)
        
        if response.status_code == 200:
            print(f"✓ DeepSeek AI API连接成功")
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"  AI响应: {content}")
            return True
        else:
            print(f"✗ DeepSeek AI API连接失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ DeepSeek AI API连接失败: {str(e)}")
        return False

def check_config_files():
    """检查配置文件"""
    print("\n=== 配置文件检查 ===")
    
    config_files = [
        'config.json',
        'trading_settings.json', 
        'indicator_settings.json'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✓ {config_file} 存在且格式正确")
                
                # 显示关键配置
                if config_file == 'indicator_settings.json':
                    adx_threshold = config.get('adx_threshold', 25)
                    print(f"  ADX阈值: {adx_threshold}")
                    
            except Exception as e:
                print(f"✗ {config_file} 格式错误: {str(e)}")
        else:
            print(f"✗ {config_file} 不存在")

def test_ai_trigger_logic():
    """测试AI触发逻辑"""
    print("\n=== AI触发逻辑测试 ===")

    # 模拟价格变化触发条件
    print("模拟价格变化触发条件:")

    # 测试多种场景
    test_scenarios = [
        {
            "name": "小幅波动",
            "current_price": 50000.0,
            "last_trigger_price": 49950.0,
            "trigger_threshold": 0.25
        },
        {
            "name": "达到阈值",
            "current_price": 50000.0,
            "last_trigger_price": 49875.0,  # 0.25%变化
            "trigger_threshold": 0.25
        },
        {
            "name": "大幅波动",
            "current_price": 50000.0,
            "last_trigger_price": 49000.0,
            "trigger_threshold": 0.25
        }
    ]

    all_passed = True

    for scenario in test_scenarios:
        print(f"\n  场景: {scenario['name']}")
        current_price = scenario['current_price']
        last_trigger_price = scenario['last_trigger_price']
        trigger_threshold = scenario['trigger_threshold']

        # 计算价格变化百分比
        price_change = abs((current_price - last_trigger_price) / last_trigger_price * 100)

        print(f"    当前价格: ${current_price}")
        print(f"    上次触发价格: ${last_trigger_price}")
        print(f"    价格变化: {price_change:.2f}%")
        print(f"    触发阈值: {trigger_threshold}%")

        should_trigger = price_change >= trigger_threshold

        if should_trigger:
            print(f"    ✓ 应该触发AI分析")
        else:
            print(f"    ✗ 不应该触发AI分析")

    return True

def monitor_real_time_trigger(api_key, secret_key, passphrase):
    """实时监控AI触发条件"""
    print("\n=== 实时触发监控 ===")
    print("监控BTC-USDT-SWAP价格变化，按Ctrl+C停止...")

    try:
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'swap',
                'adjustForTimeDifference': True,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'recvWindow': 10000
            }
        })

        last_trigger_price = None
        trigger_threshold = 0.25  # 默认阈值

        print(f"触发阈值: {trigger_threshold}%")
        print("时间\t\t价格\t\t变化%\t\t状态")
        print("-" * 60)

        while True:
            try:
                ticker = exchange.fetch_ticker('BTC-USDT-SWAP')
                current_price = round(ticker['last'], 2)
                current_time = time.strftime("%H:%M:%S")

                if last_trigger_price is not None:
                    price_change = abs((current_price - last_trigger_price) / last_trigger_price * 100)
                    price_change = round(price_change, 2)

                    if price_change >= trigger_threshold:
                        status = "🔥 触发AI分析"
                        last_trigger_price = current_price  # 重置触发价格
                    else:
                        status = "⏳ 等待中"
                else:
                    price_change = 0.0
                    status = "🎯 初始化"
                    last_trigger_price = current_price

                print(f"{current_time}\t${current_price}\t{price_change:.2f}%\t\t{status}")

                time.sleep(5)  # 每5秒检查一次

            except KeyboardInterrupt:
                print("\n监控已停止")
                break
            except Exception as e:
                print(f"监控错误: {str(e)}")
                time.sleep(10)

    except Exception as e:
        print(f"监控初始化失败: {str(e)}")

def main():
    """主函数"""
    print("AI触发分析功能检查")
    print("=" * 50)
    
    # 检查环境配置
    env_config = check_environment()
    
    # 检查各个API连接
    okx_ok = False
    news_ok = False
    ai_ok = False

    if env_config['okx_api_key'] and env_config['okx_secret_key'] and env_config['okx_passphrase']:
        okx_ok = check_okx_connection(
            env_config['okx_api_key'],
            env_config['okx_secret_key'],
            env_config['okx_passphrase']
        )
    
    if env_config['news_api_key']:
        news_ok = check_news_api(env_config['news_api_key'])
    
    if env_config['deepseek_api_key']:
        ai_ok = check_deepseek_api(env_config['deepseek_api_key'])
    
    # 检查配置文件
    check_config_files()
    
    # 测试触发逻辑
    trigger_ok = test_ai_trigger_logic()
    
    # 总结
    print("\n=== 检查总结 ===")
    print(f"OKX连接: {'✓' if okx_ok else '✗'}")
    print(f"News API: {'✓' if news_ok else '✗'}")
    print(f"DeepSeek AI: {'✓' if ai_ok else '✗'}")
    print(f"触发逻辑: {'✓' if trigger_ok else '✗'}")

    all_ok = okx_ok and news_ok and ai_ok
    
    if all_ok:
        print("\n🎉 AI触发分析功能应该正常工作！")
        print("\n建议检查项目:")
        print("1. 确保在主程序中启动了自动交易")
        print("2. 检查触发阈值设置是否合理")
        print("3. 观察价格波动是否达到触发条件")

        # 询问是否进行实时监控
        print("\n是否要进行实时触发监控？(y/n): ", end="")
        try:
            choice = input().lower().strip()
            if choice == 'y' or choice == 'yes':
                monitor_real_time_trigger(
                    env_config['okx_api_key'],
                    env_config['okx_secret_key'],
                    env_config['okx_passphrase']
                )
        except KeyboardInterrupt:
            print("\n程序已退出")
    else:
        print("\n⚠️  AI触发分析功能存在问题，请修复以上错误")

        if not okx_ok:
            print("- 检查OKX API密钥和网络连接")
        if not news_ok:
            print("- 检查News API密钥和配额")
        if not ai_ok:
            print("- 检查DeepSeek API密钥和余额")

if __name__ == "__main__":
    main()
