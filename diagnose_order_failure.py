#!/usr/bin/env python3
"""
OKX 下单失败诊断脚本
用于详细诊断下单失败的具体原因
"""

import os
import ccxt
import json
import time
from dotenv import load_dotenv
from datetime import datetime

def diagnose_okx_order_failure():
    """诊断 OKX 下单失败的具体原因"""
    print("🔍 OKX 下单失败详细诊断")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    if not all([api_key, secret_key, passphrase]):
        print("❌ 请确保在.env文件中配置了所有必需的API密钥")
        return False
    
    try:
        # 初始化OKX交易所
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': False,  # 使用生产环境
            'options': {
                'defaultType': 'swap',  # 永续合约
                'adjustForTimeDifference': True,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'recvWindow': 10000
            }
        })
        
        print("✅ 交易所连接成功")
        
        # 测试交易对
        symbol = 'BTC-USDT-SWAP'
        
        # 1. 检查市场信息
        print(f"\n📊 检查市场信息...")
        try:
            market = exchange.market(symbol)
            print(f"✅ 市场信息获取成功:")
            print(f"   交易对: {market['id']}")
            print(f"   基础货币: {market['base']}")
            print(f"   报价货币: {market['quote']}")
            print(f"   最小交易量: {market['limits']['amount']['min']}")
            print(f"   价格精度: {market['precision']['price']}")
            print(f"   数量精度: {market['precision']['amount']}")
        except Exception as e:
            print(f"❌ 获取市场信息失败: {str(e)}")
            return False
        
        # 2. 检查当前价格
        print(f"\n💰 检查当前价格...")
        try:
            ticker = exchange.fetch_ticker(symbol)
            current_price = ticker['last']
            print(f"✅ 当前价格: ${current_price}")
        except Exception as e:
            print(f"❌ 获取价格失败: {str(e)}")
            return False
        
        # 3. 检查账户信息
        print(f"\n👤 检查账户信息...")
        try:
            balance = exchange.fetch_balance()
            usdt_balance = balance.get('USDT', {}).get('free', 0)
            print(f"✅ USDT 可用余额: {usdt_balance}")
            
            # 检查账户模式
            account_info = exchange.fetch_account()
            print(f"✅ 账户信息获取成功")
            print(f"   账户类型: {account_info.get('type', '未知')}")
        except Exception as e:
            print(f"❌ 获取账户信息失败: {str(e)}")
            return False
        
        # 4. 测试杠杆设置
        print(f"\n⚖️ 测试杠杆设置...")
        try:
            # 尝试设置杠杆
            exchange.set_leverage(2, symbol, params={
                'mgnMode': 'isolated',
                'posSide': 'net'
            })
            print("✅ 杠杆设置成功")
        except Exception as e:
            print(f"⚠️  杠杆设置失败: {str(e)}")
            # 尝试其他方法
            try:
                exchange.set_leverage(2, symbol, params={'mgnMode': 'isolated'})
                print("✅ 杠杆设置成功 (方法2)")
            except Exception as e2:
                print(f"❌ 杠杆设置完全失败: {str(e2)}")
        
        # 5. 测试不同的下单参数组合
        print(f"\n📝 测试下单参数组合...")
        
        test_amount = 0.001  # 最小交易量
        order_id = f"TEST{int(time.time() * 1000)}"
        
        # 测试参数组合
        param_combinations = [
            {
                "name": "基础参数",
                "params": {
                    "marginMode": "isolated",
                    "newClientOrderId": order_id + "_1"
                }
            },
            {
                "name": "包含类型参数",
                "params": {
                    "marginMode": "isolated",
                    "type": "MARKET",
                    "newClientOrderId": order_id + "_2"
                }
            },
            {
                "name": "最简参数",
                "params": {
                    "newClientOrderId": order_id + "_3"
                }
            },
            {
                "name": "空参数",
                "params": {}
            }
        ]
        
        for i, combo in enumerate(param_combinations):
            print(f"\n   测试组合 {i+1}: {combo['name']}")
            print(f"   参数: {combo['params']}")
            
            try:
                # 模拟下单调用（不实际执行）
                print("   ✅ 参数格式正确")
                
                # 如果您想要实际测试下单，请取消下面的注释
                # 警告：这会实际下单，请确保您了解风险
                """
                order = exchange.create_order(
                    symbol=symbol,
                    type='market',
                    side='buy',
                    amount=test_amount,
                    price=None,
                    params=combo['params']
                )
                print(f"   ✅ 下单成功: {order['id']}")
                
                # 立即取消订单（如果是限价单）
                if order['status'] == 'open':
                    exchange.cancel_order(order['id'], symbol)
                    print(f"   ✅ 订单已取消")
                """
                
            except Exception as e:
                print(f"   ❌ 参数测试失败: {str(e)}")
                if hasattr(e, 'response') and e.response:
                    try:
                        error_detail = json.loads(e.response.text)
                        print(f"   详细错误: {json.dumps(error_detail, indent=2)}")
                    except:
                        print(f"   原始响应: {e.response.text}")
        
        # 6. 检查持仓信息
        print(f"\n📈 检查持仓信息...")
        try:
            positions = exchange.fetch_positions([symbol])
            print(f"✅ 持仓信息获取成功，共 {len(positions)} 个持仓")
            for pos in positions:
                if pos['contracts'] != 0:
                    print(f"   持仓: {pos['side']} {pos['contracts']} {pos['symbol']}")
        except Exception as e:
            print(f"❌ 获取持仓信息失败: {str(e)}")
        
        # 7. 检查订单历史
        print(f"\n📋 检查最近订单...")
        try:
            orders = exchange.fetch_orders(symbol, limit=5)
            print(f"✅ 订单历史获取成功，共 {len(orders)} 个订单")
            for order in orders[-3:]:  # 显示最近3个订单
                print(f"   订单: {order['id']} | {order['side']} | {order['status']} | {order['datetime']}")
        except Exception as e:
            print(f"❌ 获取订单历史失败: {str(e)}")
        
        print(f"\n🎉 诊断完成！")
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        if hasattr(e, 'response') and e.response:
            try:
                error_detail = json.loads(e.response.text)
                print(f"详细错误: {json.dumps(error_detail, indent=2)}")
            except:
                print(f"原始响应: {e.response.text}")
        return False

def show_common_solutions():
    """显示常见问题解决方案"""
    print(f"\n💡 常见问题解决方案:")
    print("=" * 60)
    
    solutions = [
        {
            "问题": "Parameter posSide error",
            "解决方案": [
                "确保下单参数中不包含 posSide",
                "仅在杠杆设置时使用 posSide: 'net'",
                "检查账户是否使用单向持仓模式"
            ]
        },
        {
            "问题": "Insufficient balance",
            "解决方案": [
                "检查 USDT 余额是否足够",
                "确认使用正确的账户模式",
                "检查是否有足够的保证金"
            ]
        },
        {
            "问题": "Symbol not found",
            "解决方案": [
                "使用正确的交易对格式: BTC-USDT-SWAP",
                "确认交易对在 OKX 上可用",
                "检查交易对是否已暂停交易"
            ]
        },
        {
            "问题": "Order size too small",
            "解决方案": [
                "增加交易数量到最小限制以上",
                "检查市场的最小交易量要求",
                "确认价格和数量的精度设置"
            ]
        }
    ]
    
    for solution in solutions:
        print(f"\n🔧 {solution['问题']}:")
        for i, fix in enumerate(solution['解决方案'], 1):
            print(f"   {i}. {fix}")

if __name__ == "__main__":
    success = diagnose_okx_order_failure()
    show_common_solutions()
    
    if success:
        print(f"\n✅ 诊断完成！请查看上述信息找出问题所在。")
    else:
        print(f"\n❌ 诊断过程中遇到问题，请检查 API 配置。")
    
    print(f"\n📝 下一步建议:")
    print("1. 如果要实际测试下单，请取消脚本中的注释")
    print("2. 从最小交易量开始测试")
    print("3. 检查应用程序的日志输出")
    print("4. 确认所有修复都已应用到主程序")
