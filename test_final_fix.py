#!/usr/bin/env python3
"""
OKX下单问题最终修复测试脚本

测试修复后的多重下单方式
"""

import os
import sys
import ccxt
import time
from dotenv import load_dotenv

def test_okx_order_methods():
    """测试OKX下单的多种方法"""
    print("🚀 开始测试OKX下单修复方案...")
    
    # 加载环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    if not all([api_key, secret_key, passphrase]):
        print("❌ 请确保设置了OKX API密钥环境变量")
        return False
    
    try:
        # 初始化OKX交易所
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': False,
            'options': {
                'defaultType': 'swap',
                'adjustForTimeDifference': True,
            }
        })
        
        print("✅ 交易所连接成功")
        
        # 测试交易对
        symbol = 'BTC-USDT-SWAP'
        
        # 获取当前价格
        ticker = exchange.fetch_ticker(symbol)
        current_price = ticker['last']
        print(f"✅ {symbol} 当前价格: ${current_price}")
        
        # 测试参数
        test_amount = 0.001  # 非常小的数量用于测试
        order_id = f"test_{int(time.time() * 1000)}"
        
        print(f"\n📝 测试下单参数构造...")
        
        # 方法1: 最简化参数
        print(f"\n🔧 方法1: 最简化参数")
        order_params_1 = {
            'clOrdId': order_id + "_1"
        }
        print(f"   参数: {order_params_1}")
        print(f"   说明: 只包含客户端订单ID，避免所有可能的参数冲突")
        
        # 方法2: 添加tdMode参数
        print(f"\n🔧 方法2: 添加tdMode参数")
        order_params_2 = {
            'clOrdId': order_id + "_2",
            'tdMode': 'isolated'
        }
        print(f"   参数: {order_params_2}")
        print(f"   说明: 添加交易模式参数，指定逐仓模式")
        
        # 方法3: ccxt标准方式
        print(f"\n🔧 方法3: ccxt标准方式")
        print(f"   参数: 无额外参数")
        print(f"   说明: 使用ccxt库的标准下单方式，不传入任何额外参数")
        
        # 对比修复前的参数
        print(f"\n❌ 修复前的错误参数:")
        old_params = {
            'marginMode': 'isolated',
            'newClientOrderId': order_id,
            'posSide': 'long'  # 这个参数导致错误
        }
        print(f"   参数: {old_params}")
        print(f"   错误: Parameter posSide error")
        
        print(f"\n✅ 修复策略:")
        print(f"   1. 完全移除posSide参数")
        print(f"   2. 使用正确的参数名称 (clOrdId 而不是 newClientOrderId)")
        print(f"   3. 使用正确的交易模式参数 (tdMode 而不是 marginMode)")
        print(f"   4. 多重尝试机制，从最简单的参数开始")
        
        print(f"\n🎯 预期效果:")
        print(f"   ✅ 方法1应该能够成功下单（如果账户配置正确）")
        print(f"   ✅ 如果方法1失败，方法2会尝试添加tdMode参数")
        print(f"   ✅ 如果方法2失败，方法3会使用ccxt标准方式")
        print(f"   ✅ 避免'Parameter posSide error'错误")
        
        print(f"\n⚠️  注意事项:")
        print(f"   - 此脚本不会实际下单，只是验证参数构造")
        print(f"   - 实际下单需要在主程序中进行")
        print(f"   - 建议先用最小金额测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_okx_order_methods()
    if success:
        print(f"\n✅ 测试完成，修复方案应该能够解决下单失败问题")
        print(f"💡 建议: 在实际使用前，先用小额进行测试")
    else:
        print(f"\n❌ 测试失败，请检查API配置和网络连接")
    
    sys.exit(0 if success else 1)
