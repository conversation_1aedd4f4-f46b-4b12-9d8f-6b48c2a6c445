#!/usr/bin/env python3
"""
检查OKX账户配置
"""

import os
import ccxt
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 获取API密钥
api_key = os.getenv('OKX_API_KEY')
secret_key = os.getenv('OKX_SECRET_KEY')
passphrase = os.getenv('OKX_PASSPHRASE')

print("🚀 开始检查OKX账户配置...")

if not all([api_key, secret_key, passphrase]):
    print("❌ 请确保设置了OKX API密钥环境变量")
    exit(1)

try:
    # 初始化OKX交易所
    exchange = ccxt.okx({
        'apiKey': api_key,
        'secret': secret_key,
        'password': passphrase,
        'enableRateLimit': True,
        'sandbox': False,
        'options': {
            'defaultType': 'swap',
            'adjustForTimeDifference': True,
        }
    })
    
    print("✅ 交易所连接成功")
    
    # 获取账户配置
    print("\n📋 获取账户配置...")
    account_config = exchange.private_get_account_config()
    print(f"账户配置响应: {account_config}")
    
    if 'data' in account_config and len(account_config['data']) > 0:
        config_data = account_config['data'][0]
        pos_mode = config_data.get('posMode', 'unknown')
        print(f"\n✅ 持仓模式: {pos_mode}")
        
        if pos_mode == 'long_short_mode':
            print("   📋 双向持仓模式 - 下单时需要指定posSide参数")
        elif pos_mode == 'net_mode':
            print("   📋 单向持仓模式 - 下单时不能指定posSide参数")
        else:
            print(f"   ⚠️  未知持仓模式: {pos_mode}")
    else:
        print("❌ 无法获取账户配置数据")
        
except Exception as e:
    print(f"❌ 错误: {str(e)}")
    import traceback
    traceback.print_exc()
