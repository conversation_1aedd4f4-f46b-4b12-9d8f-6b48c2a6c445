#!/usr/bin/env python3
"""
测试OKX下单修复
"""

import os
import ccxt
from dotenv import load_dotenv

def test_okx_order():
    """测试OKX下单功能"""
    
    # 加载环境变量
    load_dotenv()
    
    # 初始化OKX交易所
    exchange = ccxt.okx({
        'apiKey': os.getenv('OKX_API_KEY'),
        'secret': os.getenv('OKX_SECRET_KEY'),
        'password': os.getenv('OKX_PASSPHRASE'),
        'enableRateLimit': True,
        'sandbox': False,
        'options': {
            'defaultType': 'swap',
            'adjustForTimeDifference': True,
            'warnOnFetchOpenOrdersWithoutSymbol': False,
            'recvWindow': 10000
        }
    })
    
    # 测试参数
    symbol = 'BTC-USDT-SWAP'
    side = 'buy'
    amount = 0.001
    order_type = 'market'
    
    print(f"测试下单参数:")
    print(f"交易对: {symbol}")
    print(f"方向: {side}")
    print(f"数量: {amount}")
    print(f"类型: {order_type}")
    print("-" * 50)
    
    # 方法1: 使用tdMode参数
    try:
        print("方法1: 使用tdMode参数")
        params = {
            "tdMode": "isolated",
            "newClientOrderId": "test001"
        }
        
        order = exchange.create_order(
            symbol=symbol,
            type=order_type,
            side=side,
            amount=amount,
            params=params
        )
        print("✅ 方法1成功!")
        print(f"订单ID: {order.get('id', 'N/A')}")
        return True
        
    except Exception as e1:
        print(f"❌ 方法1失败: {str(e1)}")
        
        # 方法2: 使用OKX原生参数
        try:
            print("\n方法2: 使用OKX原生参数")
            params = {
                "tdMode": "isolated",
                "clOrdId": "test002"
            }
            
            order = exchange.create_order(
                symbol=symbol,
                type=order_type,
                side=side,
                amount=amount,
                params=params
            )
            print("✅ 方法2成功!")
            print(f"订单ID: {order.get('id', 'N/A')}")
            return True
            
        except Exception as e2:
            print(f"❌ 方法2失败: {str(e2)}")
            
            # 方法3: 无额外参数
            try:
                print("\n方法3: 无额外参数")
                
                order = exchange.create_order(
                    symbol=symbol,
                    type=order_type,
                    side=side,
                    amount=amount
                )
                print("✅ 方法3成功!")
                print(f"订单ID: {order.get('id', 'N/A')}")
                return True
                
            except Exception as e3:
                print(f"❌ 方法3失败: {str(e3)}")
                print(f"\n所有方法都失败了!")
                return False

def test_market_info():
    """测试获取市场信息"""
    
    load_dotenv()
    
    exchange = ccxt.okx({
        'apiKey': os.getenv('OKX_API_KEY'),
        'secret': os.getenv('OKX_SECRET_KEY'),
        'password': os.getenv('OKX_PASSPHRASE'),
        'enableRateLimit': True,
        'sandbox': False,
        'options': {
            'defaultType': 'swap'
        }
    })
    
    try:
        print("\n" + "="*50)
        print("测试获取市场信息")
        print("="*50)
        
        # 获取BTC永续合约信息
        symbol = 'BTC-USDT-SWAP'
        market = exchange.market(symbol)
        
        print(f"交易对: {symbol}")
        print(f"最小交易量: {market['limits']['amount']['min']}")
        print(f"最大交易量: {market['limits']['amount']['max']}")
        print(f"价格精度: {market['precision']['price']}")
        print(f"数量精度: {market['precision']['amount']}")
        
        # 获取当前价格
        ticker = exchange.fetch_ticker(symbol)
        print(f"当前价格: {ticker['last']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取市场信息失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试OKX下单修复")
    print("="*50)
    
    # 首先测试市场信息
    if test_market_info():
        print("\n✅ 市场信息获取成功")
        
        # 然后测试下单（注意：这会实际下单，请谨慎使用）
        print("\n⚠️  警告: 即将进行实际下单测试!")
        print("如果不想实际下单，请按 Ctrl+C 退出")
        
        # 取消实际下单测试，避免意外交易
        print("❌ 为安全起见，跳过实际下单测试")
        print("如需测试下单，请手动启用 test_okx_order() 函数")
        
        # test_okx_order()
    else:
        print("\n❌ 市场信息获取失败，无法继续测试")
