#!/usr/bin/env python3
"""
OKX杠杆设置测试脚本
用于测试OKX永续合约杠杆设置功能
"""

import os
import ccxt
from dotenv import load_dotenv

def test_okx_leverage():
    """测试OKX杠杆设置"""
    print("🚀 OKX杠杆设置测试")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    if not all([api_key, secret_key, passphrase]):
        print("❌ 请确保在.env文件中配置了所有必需的API密钥")
        return False
    
    try:
        # 初始化OKX交易所
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': False,
            'options': {
                'defaultType': 'swap',
                'adjustForTimeDifference': True,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'recvWindow': 10000
            }
        })
        
        print("✅ 交易所连接成功")
        
        # 测试交易对
        symbol = 'BTC-USDT-SWAP'
        leverage = 2
        
        print(f"\n📊 测试交易对: {symbol}")
        print(f"🎯 目标杠杆: {leverage}x")
        
        # 测试不同的杠杆设置方法
        methods = [
            {
                'name': '方法1: 标准参数',
                'params': {
                    'marginMode': 'isolated',
                    'posSide': 'net'
                }
            },
            {
                'name': '方法2: 只有marginMode',
                'params': {
                    'marginMode': 'isolated'
                }
            },
            {
                'name': '方法3: 无额外参数',
                'params': {}
            },
            {
                'name': '方法4: 使用mgnMode',
                'params': {
                    'mgnMode': 'isolated',
                    'posSide': 'net'
                }
            },
            {
                'name': '方法5: 使用instId',
                'params': {
                    'instId': symbol,
                    'mgnMode': 'isolated',
                    'posSide': 'net'
                }
            }
        ]
        
        success_method = None
        
        for i, method in enumerate(methods, 1):
            print(f"\n🧪 测试{method['name']}...")
            try:
                if method['params']:
                    exchange.set_leverage(leverage, symbol, method['params'])
                else:
                    exchange.set_leverage(leverage, symbol)
                
                print(f"✅ {method['name']} 成功！")
                success_method = method
                break
                
            except Exception as e:
                print(f"❌ {method['name']} 失败: {str(e)}")
                
                # 解析错误信息
                error_str = str(e)
                if 'posSide' in error_str:
                    print("   💡 提示: posSide参数有问题")
                elif 'marginMode' in error_str:
                    print("   💡 提示: marginMode参数有问题")
                elif 'instId' in error_str:
                    print("   💡 提示: instId参数有问题")
                elif '51000' in error_str:
                    print("   💡 提示: 参数错误，可能需要调整参数格式")
        
        if success_method:
            print(f"\n🎉 找到可用方法: {success_method['name']}")
            print(f"📋 成功参数: {success_method['params']}")
            
            # 验证杠杆是否真的设置成功
            try:
                print(f"\n🔍 验证杠杆设置...")
                # 注意：OKX可能没有直接的get_leverage方法，我们尝试获取持仓信息
                positions = exchange.fetch_positions([symbol])
                if positions:
                    for pos in positions:
                        if pos['symbol'] == symbol:
                            current_leverage = pos.get('leverage', 'N/A')
                            print(f"✅ 当前杠杆: {current_leverage}x")
                            break
                else:
                    print("ℹ️  暂无持仓，无法验证杠杆设置")
                    
            except Exception as e:
                print(f"⚠️  验证杠杆失败: {str(e)}")
            
            return True
        else:
            print(f"\n❌ 所有方法都失败了")
            print(f"\n💡 建议:")
            print("1. 检查账户是否开通了合约交易权限")
            print("2. 确认API密钥有合约交易权限")
            print("3. 检查账户模式是否支持杠杆交易")
            print("4. 尝试在OKX网页端手动设置杠杆")
            
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return False

def show_okx_leverage_guide():
    """显示OKX杠杆设置指南"""
    print(f"\n📚 OKX杠杆设置指南:")
    print("=" * 50)
    
    print("1. 账户要求:")
    print("   ✅ 完成实名认证")
    print("   ✅ 开通合约交易权限")
    print("   ✅ API密钥有交易权限")
    
    print("\n2. 账户模式:")
    print("   ✅ 单币种保证金模式")
    print("   ✅ 跨币种保证金模式")
    print("   ❌ 简单交易模式（不支持杠杆）")
    
    print("\n3. 持仓模式:")
    print("   ✅ 单向持仓模式 (posSide: net)")
    print("   ✅ 双向持仓模式 (posSide: long/short)")
    
    print("\n4. 常见错误:")
    print("   错误: Parameter posSide error")
    print("   原因: 持仓模式参数不正确")
    print("   解决: 使用正确的posSide值")
    
    print("\n5. 参数格式:")
    print("   marginMode: 'isolated' (逐仓) 或 'cross' (全仓)")
    print("   posSide: 'net' (单向) 或 'long'/'short' (双向)")

def main():
    """主函数"""
    show_okx_leverage_guide()
    
    print(f"\n" + "=" * 50)
    choice = input("是否运行OKX杠杆设置测试？(y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        success = test_okx_leverage()
        
        if success:
            print(f"\n🎉 杠杆设置测试成功！")
            print("现在可以在量化交易机器人中正常使用杠杆功能了。")
        else:
            print(f"\n❌ 杠杆设置测试失败")
            print("请检查账户设置和API权限后重试。")
    else:
        print("测试已取消。")

if __name__ == "__main__":
    main()
