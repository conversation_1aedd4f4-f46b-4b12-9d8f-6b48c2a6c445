#!/usr/bin/env python3
"""
OKX下单问题修复测试脚本

测试修复后的下单参数是否符合OKX API要求
"""

import os
import sys
import ccxt
import time
from dotenv import load_dotenv

def test_okx_order_parameters():
    """测试OKX下单参数"""
    print("🚀 开始测试OKX下单参数修复...")
    
    # 加载环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    if not all([api_key, secret_key, passphrase]):
        print("❌ 请确保设置了OKX API密钥环境变量")
        return False
    
    try:
        # 初始化OKX交易所
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': False,  # 使用生产环境
            'options': {
                'defaultType': 'swap',  # 永续合约
                'adjustForTimeDifference': True,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'recvWindow': 10000
            }
        })
        
        print("✅ 交易所连接成功")
        
        # 测试交易对
        symbol = 'BTC-USDT-SWAP'
        
        # 1. 测试获取市场信息
        print(f"\n📊 测试市场信息获取...")
        try:
            ticker = exchange.fetch_ticker(symbol)
            current_price = ticker['last']
            print(f"✅ {symbol} 当前价格: ${current_price}")
        except Exception as e:
            print(f"❌ 获取市场信息失败: {str(e)}")
            return False
        
        # 2. 测试账户信息
        print(f"\n💰 测试账户信息获取...")
        try:
            balance = exchange.fetch_balance()
            usdt_balance = balance.get('USDT', {}).get('free', 0)
            print(f"✅ USDT余额: {usdt_balance}")
            
            if usdt_balance < 10:
                print("⚠️  警告: USDT余额不足10，可能无法进行测试下单")
        except Exception as e:
            print(f"❌ 获取账户信息失败: {str(e)}")
            return False
        
        # 3. 测试杠杆设置参数
        print(f"\n⚙️ 测试杠杆设置参数...")
        try:
            # 测试修复后的杠杆设置参数
            leverage = 2
            leverage_params = {
                'mgnMode': 'isolated',
                'posSide': 'net'
            }
            
            print(f"✅ 杠杆设置参数构造成功:")
            print(f"   杠杆倍数: {leverage}")
            print(f"   参数: {leverage_params}")
            
            # 注意：这里不实际设置杠杆，只是验证参数
            print("⚠️  注意: 为了安全，此脚本不会实际设置杠杆")
            
        except Exception as e:
            print(f"❌ 杠杆参数测试失败: {str(e)}")
            return False
        
        # 4. 测试账户持仓模式
        print(f"\n⚙️ 测试账户持仓模式...")
        try:
            # 获取账户配置信息
            account_config = exchange.private_get_account_config()
            pos_mode = account_config.get('data', [{}])[0].get('posMode', 'net_mode')

            print(f"✅ 账户持仓模式: {pos_mode}")

            if pos_mode == 'long_short_mode':
                print("   📋 双向持仓模式 - 下单时需要指定posSide参数")
            else:
                print("   📋 单向持仓模式 - 下单时不能指定posSide参数")

        except Exception as e:
            print(f"❌ 获取账户配置失败: {str(e)}")
            return False

        # 5. 测试下单参数（修复后的参数）
        print(f"\n📝 测试下单参数构造...")
        try:
            # 构造修复后的下单参数
            test_amount = 0.001  # 非常小的数量用于测试
            order_id = f"test_{int(time.time() * 1000)}"

            # 根据持仓模式构造参数
            order_params = {
                'clOrdId': order_id,
                'tdMode': 'isolated'
            }

            # 如果是双向持仓模式，需要添加posSide参数
            if pos_mode == 'long_short_mode':
                order_params['posSide'] = 'long'  # 假设做多
                print("✅ 双向持仓模式，添加posSide参数")
            else:
                print("✅ 单向持仓模式，不添加posSide参数")

            print("✅ 下单参数构造成功:")
            print(f"   交易对: {symbol}")
            print(f"   数量: {test_amount}")
            print(f"   修复后参数: {order_params}")

            # 对比修复前的参数
            old_params = {
                'marginMode': 'isolated',
                'newClientOrderId': order_id
            }
            print(f"   修复前参数: {old_params}")

            print("⚠️  注意: 为了安全，此脚本不会实际下单")

        except Exception as e:
            print(f"❌ 下单参数测试失败: {str(e)}")
            return False
        
        # 5. 测试止盈止损参数
        print(f"\n🛡️ 测试止盈止损参数...")
        try:
            current_price = 50000  # 假设价格
            tp_price = current_price * 1.02  # 2%止盈
            sl_price = current_price * 0.98  # 2%止损
            
            # 修复后的止盈止损参数
            tpsl_params = {
                'triggerPx': str(tp_price),  # 使用triggerPx而不是stopPrice
                'orderPx': '-1',             # 市价单使用-1
                'tdMode': 'isolated',        # 交易模式
                'reduceOnly': True           # 只减仓
            }
            
            print("✅ 止盈止损参数构造成功:")
            print(f"   止盈价格: {tp_price}")
            print(f"   止损价格: {sl_price}")
            print(f"   修复后参数: {tpsl_params}")
            
            # 对比修复前的参数
            old_tpsl_params = {
                'stopPrice': str(tp_price),
                'type': 'TAKE_PROFIT_MARKET',
                'closePosition': True,
                'workingType': 'MARK_PRICE',
                'timeInForce': 'GTC'
            }
            print(f"   修复前参数: {old_tpsl_params}")
            
        except Exception as e:
            print(f"❌ 止盈止损参数测试失败: {str(e)}")
            return False
        
        print(f"\n🎉 所有参数测试通过！")
        print(f"\n📋 修复总结:")
        print(f"   ✅ 下单参数: marginMode → tdMode")
        print(f"   ✅ 订单ID: newClientOrderId → clOrdId")
        print(f"   ✅ 止盈止损: stopPrice → triggerPx")
        print(f"   ✅ 杠杆设置: 保持mgnMode和posSide参数")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_okx_order_parameters()
    if success:
        print(f"\n✅ 测试完成，修复后的参数应该能够解决下单失败问题")
        print(f"💡 建议: 在实际使用前，先用小额进行测试")
    else:
        print(f"\n❌ 测试失败，请检查API配置和网络连接")
    
    sys.exit(0 if success else 1)
