#!/usr/bin/env python3
"""
AI交易功能全面检查脚本
用于诊断AI交易系统的所有组件是否正常工作
"""

import os
import json
import re
import requests
import ccxt
import time
from datetime import datetime
from dotenv import load_dotenv

def check_environment_setup():
    """检查环境配置"""
    print("=== 环境配置检查 ===")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_keys = {
        'OKX_API_KEY': os.getenv('OKX_API_KEY'),
        'OKX_SECRET_KEY': os.getenv('OKX_SECRET_KEY'),
        'OKX_PASSPHRASE': os.getenv('OKX_PASSPHRASE'),
        'NEWS_API_KEY': os.getenv('NEWS_API_KEY'),
        'DEEPSEEK_API_KEY': os.getenv('DEEPSEEK_API_KEY')
    }
    
    all_keys_present = True
    for key_name, key_value in api_keys.items():
        if key_value:
            print(f"✓ {key_name}: 已配置")
        else:
            print(f"✗ {key_name}: 未配置")
            all_keys_present = False
    
    return all_keys_present, api_keys

def check_ai_trading_code():
    """检查AI交易代码实现"""
    print("\n=== AI交易代码检查 ===")
    
    if not os.path.exists('main_window.py'):
        print("✗ main_window.py文件不存在")
        return False
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键AI交易功能
    checks = [
        {
            'name': '自动交易循环',
            'pattern': 'def auto_trading_loop(self):',
            'description': '自动交易主循环函数'
        },
        {
            'name': 'AI信号获取',
            'pattern': 'def get_trading_signal(self, symbol):',
            'description': 'AI交易信号获取函数'
        },
        {
            'name': 'AI订单下单',
            'pattern': 'def place_ai_order(self,',
            'description': 'AI订单执行函数'
        },
        {
            'name': '触发阈值检查',
            'pattern': 'trigger_threshold = self.trigger_threshold_spinbox.value()',
            'description': '正确获取触发阈值'
        },
        {
            'name': '价格变化计算',
            'pattern': 'price_change = abs((current_price - self.last_trigger_price)',
            'description': '价格变化百分比计算'
        },
        {
            'name': 'AI分析调用',
            'pattern': 'analysis_result = self.get_trading_signal(base_symbol)',
            'description': 'AI分析函数调用'
        },
        {
            'name': '市场状态判断',
            'pattern': 'market_state = "STRONG_UPTREND"',
            'description': '基于ADX的市场状态判断'
        },
        {
            'name': '仓位调整逻辑',
            'pattern': 'quantity_multiplier = 1.2',
            'description': '根据市场状态调整仓位'
        },
        {
            'name': '止盈止损调整',
            'pattern': 'tp_percent = tp_percent * 1.5',
            'description': '根据市场状态调整止盈止损'
        },
        {
            'name': '订单监控',
            'pattern': 'def monitor_order_status(self,',
            'description': '订单状态监控函数'
        },
        {
            'name': 'DeepSeek API调用',
            'pattern': 'api.deepseek.com',
            'description': 'DeepSeek AI API调用'
        },
        {
            'name': '新闻API调用',
            'pattern': 'newsapi.org',
            'description': 'News API调用'
        }
    ]
    
    all_passed = True
    for check in checks:
        if check['pattern'] in content:
            print(f"✓ {check['name']}: {check['description']}")
        else:
            print(f"✗ {check['name']}: 缺失 - {check['description']}")
            all_passed = False
    
    return all_passed

def test_api_connections(api_keys):
    """测试各个API连接"""
    print("\n=== API连接测试 ===")
    
    results = {}
    
    # 测试OKX API
    print("\n1. OKX API测试:")
    try:
        exchange = ccxt.okx({
            'apiKey': api_keys['OKX_API_KEY'],
            'secret': api_keys['OKX_SECRET_KEY'],
            'password': api_keys['OKX_PASSPHRASE'],
            'enableRateLimit': True,
            'sandbox': False,
            'options': {
                'defaultType': 'swap',
                'adjustForTimeDifference': True,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'recvWindow': 10000
            }
        })

        ticker = exchange.fetch_ticker('BTC-USDT-SWAP')
        print(f"✓ OKX连接成功，BTC-USDT-SWAP价格: ${ticker['last']}")
        results['okx'] = True

    except Exception as e:
        print(f"✗ OKX连接失败: {str(e)}")
        results['okx'] = False
    
    # 测试News API
    print("\n2. News API测试:")
    try:
        url = "https://newsapi.org/v2/everything"
        params = {
            'q': 'bitcoin',
            'language': 'en',
            'sortBy': 'publishedAt',
            'pageSize': 5,
            'apiKey': api_keys['NEWS_API_KEY']
        }
        
        response = requests.get(url, params=params, timeout=(10, 30))  # (连接超时, 读取超时)
        
        if response.status_code == 200:
            data = response.json()
            articles = data.get('articles', [])
            print(f"✓ News API连接成功，获取到{len(articles)}条新闻")
            results['news'] = True
        else:
            print(f"✗ News API连接失败: {response.status_code}")
            results['news'] = False
            
    except Exception as e:
        print(f"✗ News API连接失败: {str(e)}")
        results['news'] = False
    
    # 测试DeepSeek API
    print("\n3. DeepSeek AI API测试:")
    try:
        headers = {
            'Authorization': f'Bearer {api_keys["DEEPSEEK_API_KEY"]}',
            'Content-Type': 'application/json'
        }
        
        api_url = "https://api.deepseek.com/v1/chat/completions"
        api_data = {
            "model": "deepseek-chat",
            "messages": [{"role": "user", "content": "Hello, test connection"}],
            "temperature": 0.7,
            "max_tokens": 10
        }
        
        response = requests.post(api_url, json=api_data, headers=headers, timeout=30)
        
        if response.status_code == 200:
            print("✓ DeepSeek AI API连接成功")
            results['deepseek'] = True
        else:
            print(f"✗ DeepSeek AI API连接失败: {response.status_code}")
            results['deepseek'] = False
            
    except Exception as e:
        print(f"✗ DeepSeek AI API连接失败: {str(e)}")
        results['deepseek'] = False
    
    return results

def check_trading_configuration():
    """检查交易配置"""
    print("\n=== 交易配置检查 ===")
    
    config_files = [
        'config.json',
        'trading_settings.json',
        'indicator_settings.json'
    ]
    
    all_configs_ok = True
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✓ {config_file}: 格式正确")
                
                # 显示关键配置
                if config_file == 'indicator_settings.json':
                    adx_threshold = config.get('adx_threshold', 25)
                    print(f"  ADX阈值: {adx_threshold}")
                elif config_file == 'config.json':
                    tp_percent = config.get('tp_percent', 1.5)
                    sl_percent = config.get('sl_percent', 1.5)
                    print(f"  止盈: {tp_percent}%, 止损: {sl_percent}%")
                    
            except Exception as e:
                print(f"✗ {config_file}: 格式错误 - {str(e)}")
                all_configs_ok = False
        else:
            print(f"✗ {config_file}: 不存在")
            all_configs_ok = False
    
    return all_configs_ok

def test_ai_analysis_flow():
    """测试AI分析流程"""
    print("\n=== AI分析流程测试 ===")
    
    # 模拟AI分析流程的各个步骤
    steps = [
        "1. 价格变化检测 → 触发AI分析",
        "2. 获取市场新闻 → News API",
        "3. 计算技术指标 → ADX, RSI, MACD等",
        "4. 市场状态判断 → 强趋势/震荡",
        "5. AI综合分析 → DeepSeek API",
        "6. 提取交易信号 → 看多/看空",
        "7. 风险控制调整 → 仓位和止盈止损",
        "8. 执行交易订单 → OKX API",
        "9. 订单状态监控 → 实时跟踪"
    ]
    
    for step in steps:
        print(f"  {step}")
    
    print("\n关键决策点:")
    decision_points = [
        "• 触发阈值: 价格变化是否达到设定百分比",
        "• 趋势确认: 两次AI分析结果是否一致",
        "• 市场状态: ADX指标判断趋势强度",
        "• 仓位调整: 根据趋势方向和强度调整",
        "• 风险控制: 动态调整止盈止损比例"
    ]
    
    for point in decision_points:
        print(f"  {point}")

def check_ui_components():
    """检查UI组件"""
    print("\n=== UI组件检查 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    ui_components = [
        'self.trigger_threshold_spinbox',
        'self.ai_trading_symbol_combo',
        'self.trade_amount_input',
        'self.auto_trading_button',
        'self.use_ai_price_radio',
        'self.use_current_price_radio',
        'self.market_order_radio',
        'self.limit_order_radio',
        'self.ai_leverage_spinbox',
        'self.tp_spinbox',
        'self.sl_spinbox'
    ]
    
    all_ui_ok = True
    for component in ui_components:
        if component in content:
            print(f"✓ {component}: UI组件存在")
        else:
            print(f"✗ {component}: UI组件缺失")
            all_ui_ok = False
    
    return all_ui_ok

def generate_recommendations():
    """生成使用建议"""
    print("\n=== 使用建议 ===")
    
    recommendations = [
        "1. 触发阈值设置:",
        "   - 高频交易: 0.1%-0.3%",
        "   - 中频交易: 0.3%-0.8% (推荐)",
        "   - 低频交易: 0.8%-2.0%",
        "",
        "2. 风险管理:",
        "   - 建议止盈: 1.5%-3.0%",
        "   - 建议止损: 1.0%-2.0%",
        "   - 交易金额: 账户资金的1%-5%",
        "",
        "3. 市场适应:",
        "   - 高波动市场: 提高触发阈值",
        "   - 低波动市场: 降低触发阈值",
        "   - 趋势市场: 系统自动调整仓位",
        "",
        "4. 监控要点:",
        "   - 观察交易日志中的触发记录",
        "   - 检查AI分析的准确性",
        "   - 监控订单执行状态",
        "   - 定期检查API连接状态"
    ]
    
    for rec in recommendations:
        print(rec)

def main():
    """主函数"""
    print("AI交易功能全面检查")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项检查
    env_ok, api_keys = check_environment_setup()
    code_ok = check_ai_trading_code()
    
    if env_ok:
        api_ok = test_api_connections(api_keys)
        all_apis_ok = all(api_ok.values())
    else:
        api_ok = {'okx': False, 'news': False, 'deepseek': False}
        all_apis_ok = False
    
    config_ok = check_trading_configuration()
    ui_ok = check_ui_components()
    
    # 测试AI分析流程
    test_ai_analysis_flow()
    
    # 生成建议
    generate_recommendations()
    
    # 总结
    print("\n=== 检查总结 ===")
    print(f"环境配置: {'✓' if env_ok else '✗'}")
    print(f"代码实现: {'✓' if code_ok else '✗'}")
    print(f"API连接: {'✓' if all_apis_ok else '✗'}")
    print(f"  - OKX API: {'✓' if api_ok.get('okx', False) else '✗'}")
    print(f"  - News API: {'✓' if api_ok.get('news', False) else '✗'}")
    print(f"  - DeepSeek AI: {'✓' if api_ok.get('deepseek', False) else '✗'}")
    print(f"配置文件: {'✓' if config_ok else '✗'}")
    print(f"UI组件: {'✓' if ui_ok else '✗'}")
    
    overall_status = env_ok and code_ok and all_apis_ok and config_ok and ui_ok
    
    if overall_status:
        print("\n🎉 AI交易功能完全正常！")
        print("\n系统特性:")
        print("✓ 智能触发分析 - 基于价格波动自动触发")
        print("✓ 多源信息融合 - 新闻+技术指标+AI分析")
        print("✓ 动态风险控制 - 根据市场状态调整策略")
        print("✓ 实时订单监控 - 自动跟踪订单状态")
        print("✓ 完善错误处理 - 网络异常自动重试")
        
        print("\n准备开始AI交易:")
        print("1. 启动程序: python main_window.py")
        print("2. 设置触发阈值 (推荐0.25%-0.5%)")
        print("3. 配置交易金额和止盈止损")
        print("4. 点击'启动自动交易'")
        print("5. 观察交易日志和订单状态")
        
    else:
        print("\n⚠️  AI交易功能存在问题")
        
        if not env_ok:
            print("- 请配置所有必要的API密钥")
        if not code_ok:
            print("- 检查代码实现是否完整")
        if not all_apis_ok:
            print("- 修复API连接问题")
        if not config_ok:
            print("- 检查配置文件格式")
        if not ui_ok:
            print("- 检查UI组件完整性")

if __name__ == "__main__":
    main()
