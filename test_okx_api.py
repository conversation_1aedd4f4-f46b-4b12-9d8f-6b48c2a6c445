#!/usr/bin/env python3
"""
OKX API连接测试脚本
用于诊断OKX API连接问题
"""

import os
import ccxt
from dotenv import load_dotenv

def test_okx_api():
    """测试OKX API连接"""
    print("🚀 OKX API连接测试")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    print("📋 检查API密钥配置:")
    print(f"  API Key: {'✓ 已配置' if api_key else '✗ 未配置'}")
    print(f"  Secret Key: {'✓ 已配置' if secret_key else '✗ 未配置'}")
    print(f"  Passphrase: {'✓ 已配置' if passphrase else '✗ 未配置'}")
    
    if not all([api_key, secret_key, passphrase]):
        print("\n❌ 请确保在.env文件中配置了所有必需的API密钥")
        print("需要配置:")
        print("  OKX_API_KEY=your_api_key")
        print("  OKX_SECRET_KEY=your_secret_key")
        print("  OKX_PASSPHRASE=your_passphrase")
        return False
    
    print(f"\n🔑 API密钥信息:")
    print(f"  API Key: {api_key[:8]}...{api_key[-4:] if len(api_key) > 12 else api_key}")
    print(f"  Secret Key: {secret_key[:8]}...{secret_key[-4:] if len(secret_key) > 12 else secret_key}")
    print(f"  Passphrase: {'*' * len(passphrase)}")
    
    # 测试不同的配置
    configs = [
        {
            'name': '基础配置',
            'config': {
                'apiKey': api_key,
                'secret': secret_key,
                'password': passphrase,
                'enableRateLimit': True,
                'sandbox': False
            }
        },
        {
            'name': '完整配置',
            'config': {
                'apiKey': api_key,
                'secret': secret_key,
                'password': passphrase,
                'enableRateLimit': True,
                'sandbox': False,
                'options': {
                    'defaultType': 'swap',
                    'adjustForTimeDifference': True,
                    'warnOnFetchOpenOrdersWithoutSymbol': False,
                    'recvWindow': 10000
                }
            }
        }
    ]
    
    for config_info in configs:
        print(f"\n🧪 测试 {config_info['name']}:")
        try:
            exchange = ccxt.okx(config_info['config'])
            
            # 测试1: 获取市场数据（不需要认证）
            print("  📊 测试市场数据获取...")
            try:
                ticker = exchange.fetch_ticker('BTC-USDT-SWAP')
                print(f"    ✓ 市场数据获取成功: BTC-USDT-SWAP 价格 ${ticker['last']}")
            except Exception as e:
                print(f"    ✗ 市场数据获取失败: {str(e)}")
                continue
            
            # 测试2: 获取账户信息（需要认证）
            print("  🏦 测试账户信息获取...")
            try:
                balance = exchange.fetch_balance()
                print("    ✓ 账户信息获取成功")
                print(f"    💰 账户余额: {len(balance.get('info', {}))} 个币种")
                return True
            except ccxt.AuthenticationError as e:
                print(f"    ✗ 认证失败: {str(e)}")
                print("    💡 可能的原因:")
                print("       - API密钥、Secret或Passphrase不正确")
                print("       - API权限不足")
                print("       - IP地址未加入白名单")
                print("       - API密钥已过期或被禁用")
            except ccxt.PermissionDenied as e:
                print(f"    ✗ 权限被拒绝: {str(e)}")
                print("    💡 请检查API权限设置")
            except Exception as e:
                print(f"    ✗ 其他错误: {str(e)}")
                
        except Exception as e:
            print(f"  ✗ 交易所初始化失败: {str(e)}")
    
    print(f"\n📝 故障排除建议:")
    print("1. 确认API密钥是否正确复制（注意空格和特殊字符）")
    print("2. 检查OKX账户中的API权限设置")
    print("3. 确认IP地址是否在白名单中")
    print("4. 验证API密钥是否已激活且未过期")
    print("5. 检查是否在正确的环境（生产环境 vs 沙盒环境）")
    
    return False

def main():
    """主函数"""
    success = test_okx_api()
    
    if success:
        print(f"\n🎉 OKX API连接测试成功！")
        print("现在可以正常使用量化交易机器人了。")
    else:
        print(f"\n❌ OKX API连接测试失败")
        print("请根据上述建议检查配置后重试。")

if __name__ == "__main__":
    main()
