#!/usr/bin/env python3
"""
测试当前下单逻辑的脚本
模拟 main_window.py 中的 place_ai_order 函数逻辑
"""

import os
import ccxt
import time
from dotenv import load_dotenv
from datetime import datetime

def test_current_order_logic():
    """测试当前的下单逻辑"""
    print("🧪 测试当前下单逻辑")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    if not all([api_key, secret_key, passphrase]):
        print("❌ 请确保在.env文件中配置了所有必需的API密钥")
        return False
    
    try:
        # 初始化OKX交易所（模拟主程序的设置）
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': False,
            'options': {
                'defaultType': 'swap',  # 永续合约
                'adjustForTimeDifference': True,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'recvWindow': 10000
            }
        })
        
        print("✅ 交易所连接成功")
        
        # 模拟主程序的参数
        symbol = 'BTC/USDT'  # 主程序使用的格式
        side = 'buy'
        amount = 0.001
        
        # 获取当前价格
        ticker = exchange.fetch_ticker('BTC-USDT-SWAP')
        current_price = ticker['last']
        entry_price = current_price
        tp_price = current_price * 1.02  # 2% 止盈
        sl_price = current_price * 0.98  # 2% 止损
        
        print(f"📊 测试参数:")
        print(f"   原始交易对: {symbol}")
        print(f"   方向: {side}")
        print(f"   数量: {amount}")
        print(f"   当前价格: {current_price}")
        
        # === 模拟 place_ai_order 函数的逻辑 ===
        
        # 1. 价格验证
        if not all(isinstance(x, (int, float)) for x in [entry_price, tp_price, sl_price]):
            raise ValueError("价格必须是有效的数字")
        
        if entry_price <= 0 or tp_price <= 0 or sl_price <= 0:
            raise ValueError("价格必须大于0")
        
        print("✅ 价格验证通过")
        
        # 2. 交易对格式转换
        base_quote = symbol.split('/')
        base, quote = base_quote[0], base_quote[1]
        swap_symbol = f"{base}-{quote}-SWAP"
        
        print(f"✅ 交易对转换: {symbol} -> {swap_symbol}")
        
        # 3. 杠杆设置测试
        leverage = 2  # 模拟杠杆倍数
        
        print(f"⚖️ 测试杠杆设置...")
        leverage_set = False
        
        # 方法1: 使用mgnMode参数
        try:
            exchange.set_leverage(leverage, swap_symbol, params={
                'mgnMode': 'isolated',
                'posSide': 'net'
            })
            print(f"✅ 杠杆设置成功: {leverage}x")
            leverage_set = True
        except Exception as e:
            print(f"⚠️  方法1失败: {str(e)}")
        
        # 方法2: 不使用posSide参数
        if not leverage_set:
            try:
                exchange.set_leverage(leverage, swap_symbol, params={
                    'mgnMode': 'isolated'
                })
                print(f"✅ 杠杆设置成功 (方法2): {leverage}x")
                leverage_set = True
            except Exception as e:
                print(f"⚠️  方法2失败: {str(e)}")
        
        # 方法3: 使用最简参数
        if not leverage_set:
            try:
                exchange.set_leverage(leverage, swap_symbol)
                print(f"✅ 杠杆设置成功 (方法3): {leverage}x")
                leverage_set = True
            except Exception as e:
                print(f"⚠️  方法3失败: {str(e)}")
        
        if not leverage_set:
            print("⚠️  所有杠杆设置方法都失败，但继续测试下单")
        
        # 4. 获取市场信息
        market = exchange.market(swap_symbol)
        min_amount = 0.001  # OKX永续合约最小交易量
        
        # 格式化数量
        quantity = max(min_amount, round(amount / min_amount) * min_amount)
        
        print(f"✅ 数量格式化: {amount} -> {quantity}")
        
        # 5. 生成订单ID
        order_id = f"TEST{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # 6. 设置订单类型（模拟市价单）
        order_type = 'market'
        
        # 7. 构造下单参数（按照修复后的逻辑）
        params = {
            "marginMode": "isolated",  # 逐仓模式
            "newClientOrderId": order_id
            # 注意：不包含 posSide 参数
        }
        
        # 根据订单类型设置参数
        if order_type == 'market':
            params["type"] = "MARKET"
        else:  # 限价单
            params["type"] = "LIMIT"
            params["timeInForce"] = "GTC"
        
        print(f"✅ 下单参数构造完成:")
        print(f"   交易对: {swap_symbol}")
        print(f"   类型: {order_type}")
        print(f"   方向: {side}")
        print(f"   数量: {quantity}")
        print(f"   参数: {params}")
        
        # 8. 模拟下单调用（不实际执行）
        print(f"\n🚀 模拟下单调用...")
        print("   exchange.create_order(")
        print(f"       symbol='{swap_symbol}',")
        print(f"       type='{order_type}',")
        print(f"       side='{side}',")
        print(f"       amount={float(quantity)},")
        print(f"       price=None,")
        print(f"       params={params}")
        print("   )")
        
        # 如果您想要实际测试下单，请取消下面的注释
        # 警告：这会实际下单！
        """
        try:
            order = exchange.create_order(
                symbol=swap_symbol,
                type=order_type,
                side=side,
                amount=float(quantity),
                price=None,
                params=params
            )
            print(f"✅ 实际下单成功: {order['id']}")
            return True
        except Exception as e:
            print(f"❌ 实际下单失败: {str(e)}")
            if hasattr(e, 'response') and e.response:
                print(f"API响应: {e.response.text}")
            return False
        """
        
        print("✅ 模拟下单完成（未实际执行）")
        
        # 9. 检查参数是否符合OKX要求
        print(f"\n🔍 参数检查:")
        
        # 检查必需参数
        required_checks = [
            ("交易对格式", swap_symbol.endswith('-SWAP')),
            ("数量大于最小值", quantity >= min_amount),
            ("参数不包含posSide", 'posSide' not in params),
            ("包含marginMode", 'marginMode' in params),
            ("包含newClientOrderId", 'newClientOrderId' in params)
        ]
        
        all_passed = True
        for check_name, check_result in required_checks:
            if check_result:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name}")
                all_passed = False
        
        if all_passed:
            print(f"\n🎉 所有参数检查通过！")
            print("   如果仍然下单失败，可能的原因:")
            print("   1. 账户余额不足")
            print("   2. 账户模式不支持")
            print("   3. API权限不足")
            print("   4. 网络连接问题")
            print("   5. OKX服务器问题")
        else:
            print(f"\n❌ 参数检查未通过，请修复上述问题")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试过程失败: {str(e)}")
        if hasattr(e, 'response') and e.response:
            print(f"API响应: {e.response.text}")
        return False

def show_debugging_tips():
    """显示调试建议"""
    print(f"\n🔧 调试建议:")
    print("=" * 50)
    
    tips = [
        "1. 检查应用程序的交易日志，查看具体错误信息",
        "2. 确认账户有足够的USDT余额",
        "3. 验证API密钥具有交易权限",
        "4. 检查OKX账户是否使用正确的保证金模式",
        "5. 尝试在OKX网页版手动下单测试",
        "6. 检查网络连接和防火墙设置",
        "7. 查看OKX官方API文档的最新要求"
    ]
    
    for tip in tips:
        print(tip)

if __name__ == "__main__":
    success = test_current_order_logic()
    show_debugging_tips()
    
    if success:
        print(f"\n✅ 逻辑测试通过！如果实际下单仍失败，请查看上述调试建议。")
    else:
        print(f"\n❌ 逻辑测试失败，请修复发现的问题。")
    
    print(f"\n📋 下一步:")
    print("1. 如果要实际测试下单，请取消脚本中的注释")
    print("2. 查看应用程序的详细错误日志")
    print("3. 检查OKX账户设置和余额")
    print("4. 联系OKX客服确认账户状态")
