# OKX API 故障排除指南

## 🚨 常见错误："API validation failed: Invalid API Key or Secret"

这个错误通常表示API认证失败。以下是详细的排查步骤：

## 📋 检查清单

### 1. API密钥配置检查

**步骤1: 验证.env文件**
```bash
# 检查.env文件是否存在
ls -la .env

# 查看.env文件内容（注意：不要在公共场所执行此命令）
cat .env
```

**必需的配置项：**
```
OKX_API_KEY=your_api_key_here
OKX_SECRET_KEY=your_secret_key_here
OKX_PASSPHRASE=your_passphrase_here
```

**步骤2: 验证API密钥格式**
- API Key: 通常是32位字符串
- Secret Key: 通常是44位字符串（Base64编码）
- Passphrase: 您在创建API时设置的密码短语

### 2. OKX账户设置检查

**步骤1: 登录OKX官网**
1. 访问 https://www.okx.com
2. 登录您的账户
3. 进入 "API管理" 页面

**步骤2: 检查API权限**
确保您的API密钥具有以下权限：
- ✅ 读取权限 (Read)
- ✅ 交易权限 (Trade) - 如果需要自动交易
- ✅ 资产权限 (Withdraw) - 可选

**步骤3: 检查IP白名单**
- 如果设置了IP白名单，确保当前IP地址在列表中
- 建议：如果是测试阶段，可以暂时不设置IP限制

**步骤4: 检查API状态**
- 确认API密钥状态为"已激活"
- 检查是否有过期时间设置

### 3. 网络和环境检查

**步骤1: 测试网络连接**
```bash
# 测试是否能访问OKX API
curl -I https://www.okx.com/api/v5/public/time
```

**步骤2: 运行API测试脚本**
```bash
python test_okx_api.py
```

## 🔧 具体解决方案

### 方案1: 重新创建API密钥

1. **删除现有API密钥**
   - 在OKX官网删除当前的API密钥

2. **创建新的API密钥**
   - 设置合适的权限
   - 记录新的API Key、Secret Key和Passphrase
   - 暂时不设置IP白名单

3. **更新.env文件**
   ```
   OKX_API_KEY=新的API密钥
   OKX_SECRET_KEY=新的Secret密钥
   OKX_PASSPHRASE=新的密码短语
   ```

### 方案2: 检查字符编码问题

**常见问题：**
- 复制粘贴时包含了不可见字符
- 文件编码不是UTF-8

**解决方法：**
```bash
# 检查.env文件编码
file .env

# 如果不是UTF-8，转换编码
iconv -f ISO-8859-1 -t UTF-8 .env > .env.new
mv .env.new .env
```

### 方案3: 使用沙盒环境测试

如果生产环境有问题，可以先在沙盒环境测试：

1. **申请沙盒API密钥**
   - 访问 OKX 沙盒环境
   - 创建测试API密钥

2. **修改代码使用沙盒**
   ```python
   # 在login.py中临时修改
   'sandbox': True,  # 改为True使用沙盒环境
   ```

## 🧪 测试步骤

### 步骤1: 基础连接测试
```bash
python test_okx_api.py
```

### 步骤2: 登录界面测试
1. 启动程序：`python main_window.py`
2. 在登录界面输入API密钥
3. 点击登录按钮
4. 查看错误信息

### 步骤3: 详细日志检查
如果仍有问题，可以启用详细日志：

```python
# 在login.py的validate_credentials函数中添加
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📞 获取帮助

如果以上步骤都无法解决问题，请提供以下信息：

1. **错误信息截图**
2. **API测试脚本输出**
3. **OKX API权限设置截图**
4. **网络环境信息**

## ⚠️ 安全提醒

- 🔒 永远不要在公共场所或聊天中分享您的API密钥
- 🔄 定期更换API密钥
- 🛡️ 设置合适的IP白名单
- 💰 使用小额资金进行测试
- 📊 定期检查交易记录

## 🎯 成功标志

当API配置正确时，您应该看到：
- ✅ "API credentials verified successfully!"
- ✅ 能够获取账户余额信息
- ✅ 能够获取市场数据
- ✅ 登录界面显示绿色验证标志
