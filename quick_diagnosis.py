#!/usr/bin/env python3
"""
快速诊断脚本 - 帮助用户快速定位OKX API问题
"""

import os
import sys
from dotenv import load_dotenv

def check_env_file():
    """检查.env文件"""
    print("🔍 检查.env文件...")
    
    if not os.path.exists('.env'):
        print("❌ .env文件不存在")
        print("💡 解决方案:")
        print("   1. 复制.env.example为.env: cp .env.example .env")
        print("   2. 编辑.env文件，填入您的API密钥")
        return False
    
    print("✅ .env文件存在")
    
    # 检查文件内容
    load_dotenv()
    
    required_keys = ['OKX_API_KEY', 'OKX_SECRET_KEY', 'OKX_PASSPHRASE']
    missing_keys = []
    
    for key in required_keys:
        value = os.getenv(key)
        if not value:
            missing_keys.append(key)
        elif value.startswith('your_'):
            print(f"⚠️  {key}: 使用默认值，需要替换为真实密钥")
            missing_keys.append(key)
        else:
            print(f"✅ {key}: 已配置")
    
    if missing_keys:
        print(f"❌ 缺少或未正确配置的密钥: {', '.join(missing_keys)}")
        return False
    
    return True

def check_api_key_format():
    """检查API密钥格式"""
    print("\n🔑 检查API密钥格式...")
    
    load_dotenv()
    
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    # 检查API Key格式
    if api_key:
        if len(api_key) < 20:
            print("⚠️  API Key长度可能不正确（通常应该是32位字符）")
        else:
            print("✅ API Key长度正常")
    
    # 检查Secret Key格式
    if secret_key:
        if len(secret_key) < 30:
            print("⚠️  Secret Key长度可能不正确（通常应该是44位字符）")
        else:
            print("✅ Secret Key长度正常")
    
    # 检查Passphrase
    if passphrase:
        if len(passphrase) < 1:
            print("❌ Passphrase不能为空")
        else:
            print("✅ Passphrase已设置")
    
    return True

def check_dependencies():
    """检查依赖库"""
    print("\n📦 检查依赖库...")
    
    try:
        import ccxt
        print("✅ ccxt库已安装")
        
        # 检查是否支持OKX
        if 'okx' in ccxt.exchanges:
            print("✅ ccxt支持OKX交易所")
        else:
            print("❌ ccxt不支持OKX交易所，请更新ccxt版本")
            return False
            
    except ImportError:
        print("❌ ccxt库未安装")
        print("💡 安装命令: pip install ccxt")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv库已安装")
    except ImportError:
        print("❌ python-dotenv库未安装")
        print("💡 安装命令: pip install python-dotenv")
        return False
    
    return True

def provide_solutions():
    """提供解决方案"""
    print("\n💡 常见问题解决方案:")
    print("\n1. API密钥问题:")
    print("   - 确保从OKX官网正确复制API密钥")
    print("   - 检查是否包含多余的空格或换行符")
    print("   - 确认API权限设置正确（读取+交易权限）")
    
    print("\n2. 网络问题:")
    print("   - 检查网络连接是否正常")
    print("   - 确认防火墙没有阻止连接")
    print("   - 如果使用代理，请配置正确")
    
    print("\n3. 账户问题:")
    print("   - 确认OKX账户已完成实名认证")
    print("   - 检查API密钥是否已激活")
    print("   - 确认IP地址在白名单中（如果设置了）")
    
    print("\n4. 下一步操作:")
    print("   - 运行: python test_okx_api.py")
    print("   - 查看: OKX_API_故障排除指南.md")
    print("   - 如果问题持续，请联系技术支持")

def main():
    """主函数"""
    print("🚀 OKX量化交易机器人 - 快速诊断")
    print("=" * 50)
    
    # 检查步骤
    steps = [
        ("环境文件", check_env_file),
        ("API密钥格式", check_api_key_format),
        ("依赖库", check_dependencies)
    ]
    
    all_passed = True
    
    for step_name, check_func in steps:
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {step_name}检查失败: {str(e)}")
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("🎉 基础检查全部通过！")
        print("如果仍有API连接问题，请运行: python test_okx_api.py")
    else:
        print("❌ 发现问题，请根据上述提示进行修复")
        provide_solutions()

if __name__ == "__main__":
    main()
