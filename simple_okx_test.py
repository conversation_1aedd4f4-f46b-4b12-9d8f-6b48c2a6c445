#!/usr/bin/env python3
"""
简单的OKX账户配置测试
"""

import os
import ccxt
from dotenv import load_dotenv

def test_okx_account_config():
    """测试OKX账户配置"""
    print("🚀 开始测试OKX账户配置...")
    
    # 加载环境变量
    load_dotenv()
    
    # 获取API密钥
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    if not all([api_key, secret_key, passphrase]):
        print("❌ 请确保设置了OKX API密钥环境变量")
        return False
    
    try:
        # 初始化OKX交易所
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': False,
            'options': {
                'defaultType': 'swap',
                'adjustForTimeDifference': True,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'recvWindow': 10000
            }
        })
        
        print("✅ 交易所连接成功")
        
        # 测试获取账户配置
        print("\n📋 获取账户配置...")
        try:
            account_config = exchange.private_get_account_config()
            print(f"✅ 账户配置获取成功")
            print(f"原始响应: {account_config}")
            
            if 'data' in account_config and len(account_config['data']) > 0:
                config_data = account_config['data'][0]
                pos_mode = config_data.get('posMode', 'unknown')
                print(f"✅ 持仓模式: {pos_mode}")
                
                if pos_mode == 'long_short_mode':
                    print("   📋 双向持仓模式 - 下单时需要指定posSide参数")
                elif pos_mode == 'net_mode':
                    print("   📋 单向持仓模式 - 下单时不能指定posSide参数")
                else:
                    print(f"   ⚠️  未知持仓模式: {pos_mode}")
                    
                # 显示其他配置信息
                print(f"\n📊 其他配置信息:")
                for key, value in config_data.items():
                    print(f"   {key}: {value}")
                    
            else:
                print("❌ 账户配置数据为空")
                return False
                
        except Exception as e:
            print(f"❌ 获取账户配置失败: {str(e)}")
            return False
        
        # 测试获取账户信息
        print(f"\n💰 测试账户信息...")
        try:
            balance = exchange.fetch_balance()
            usdt_balance = balance.get('USDT', {}).get('free', 0)
            print(f"✅ USDT余额: {usdt_balance}")
        except Exception as e:
            print(f"❌ 获取账户信息失败: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_okx_account_config()
    if success:
        print(f"\n✅ 测试完成")
    else:
        print(f"\n❌ 测试失败")
