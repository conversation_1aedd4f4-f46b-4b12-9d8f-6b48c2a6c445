"""
K线图表组件模块

该模块实现了一个基于PyQt6的K线图表组件，支持：
- 各种时间周期的K线显示
- 多种技术指标（MA、MACD、RSI、BOLL、ADX）
- 指标切换和自定义配置
- 图表主题定制

作者: [李兴]
版本: 1.0.0
创建日期: 2024
"""

from typing import Dict, List, Optional, Any, Union, Tuple, cast
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QComboBox, QLabel
from PyQt6.QtCharts import QChart, QChartView, QCandlestickSet, QCandlestickSeries, QLineSeries, QValueAxis, QDateTimeAxis
from PyQt6.QtCore import Qt, QDateTime, QPointF, pyqtSignal
from PyQt6.QtGui import QPainter, QColor, QFont, QPen, QBrush, QLinearGradient
import talib
import numpy as np
import pandas as pd

class ChartWidget(QWidget):
    """
    K线图表组件类
    
    该类实现了一个可定制的K线图表控件，支持多种技术指标和时间周期显示
    
    Attributes:
        timeframe_changed: 时间周期变化信号，通知主窗口刷新数据
    """
    
    # 添加信号以通知主窗口刷新数据
    timeframe_changed = pyqtSignal()
    
    def __init__(self, parent: Optional[QWidget] = None) -> None:
        """
        初始化图表组件
        
        Args:
            parent: 父级组件，默认为None
        """
        super().__init__(parent)
        self.init_ui()
        self.current_adx: Optional[float] = None  # 添加ADX当前值存储
        
    def init_ui(self) -> None:
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建顶部面板
        top_panel = QWidget()
        top_panel.setFixedHeight(50)
        top_panel.setStyleSheet("""
            QWidget {
                background-color: #14151a;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
        """)
        top_layout = QHBoxLayout(top_panel)
        top_layout.setContentsMargins(15, 0, 15, 0)
        
        # 技术指标选择
        indicator_layout = QHBoxLayout()
        indicator_layout.setContentsMargins(0, 0, 0, 0)
        indicator_layout.setSpacing(10)
        
        self.indicator_combo = QComboBox()
        self.indicator_combo.addItems(['MA', 'MACD', 'RSI', 'BOLL', 'ADX'])  # 添加ADX选项
        self.indicator_combo.currentTextChanged.connect(self.update_indicator)
        self.indicator_combo.setFixedWidth(120)
        self.indicator_combo.setStyleSheet("""
            QComboBox {
                padding: 5px 10px;
                border: 1px solid #2a2e30;
                border-radius: 4px;
                background-color: #1b1e22;
                color: #E6E8EA;
                font-size: 13px;
                min-height: 20px;
            }
            QComboBox::drop-down {
                border: none;
                padding-right: 15px;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
            QComboBox:hover {
                border: 1px solid #35383c;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #2a2e30;
                selection-background-color: #2a2e30;
                background-color: #14151a;
                color: #E6E8EA;
            }
        """)
        
        indicator_label = QLabel("技术指标:")
        indicator_label.setStyleSheet("color: #848E9C; font-size: 13px;")
        
        indicator_layout.addWidget(indicator_label)
        indicator_layout.addWidget(self.indicator_combo)
        
        # ADX值显示标签
        self.adx_label = QLabel("")
        self.adx_label.setStyleSheet("color: #F0B90B; font-size: 13px; font-weight: bold;")
        indicator_layout.addWidget(self.adx_label)
        
        # 时间周期选择
        timeframe_label = QLabel("时间周期:")
        timeframe_label.setStyleSheet("color: #848E9C; font-size: 13px;")
        
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems(['5分钟', '15分钟', '1小时', '4小时', '1天'])
        self.timeframe_combo.setCurrentText('15分钟')  # 默认选择15分钟
        self.timeframe_combo.setFixedWidth(120)
        self.timeframe_combo.currentTextChanged.connect(self.on_timeframe_changed)
        self.timeframe_combo.setStyleSheet("""
            QComboBox {
                padding: 5px 10px;
                border: 1px solid #2a2e30;
                border-radius: 4px;
                background-color: #1b1e22;
                color: #E6E8EA;
                font-size: 13px;
                min-height: 20px;
            }
            QComboBox::drop-down {
                border: none;
                padding-right: 15px;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
            QComboBox:hover {
                border: 1px solid #35383c;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #2a2e30;
                selection-background-color: #2a2e30;
                background-color: #14151a;
                color: #E6E8EA;
            }
        """)
        
        top_layout.addLayout(indicator_layout)
        top_layout.addSpacing(15)
        top_layout.addWidget(timeframe_label)
        top_layout.addWidget(self.timeframe_combo)
        top_layout.addStretch()
        
        # 添加顶部面板到主布局
        layout.addWidget(top_panel)
        
        # 创建图表容器面板
        chart_panel = QWidget()
        chart_panel.setStyleSheet("""
            QWidget {
                background-color: #0B0E11;
                border-bottom-left-radius: 6px;
                border-bottom-right-radius: 6px;
            }
        """)
        chart_layout = QVBoxLayout(chart_panel)
        chart_layout.setContentsMargins(1, 0, 1, 1)
        chart_layout.setSpacing(0)
        
        # 创建K线图
        self.chart = QChart()
        self.chart.setTheme(QChart.ChartTheme.ChartThemeDark)
        self.chart.setBackgroundBrush(QBrush(QColor("#0B0E11")))
        self.chart.setAnimationOptions(QChart.AnimationOption.SeriesAnimations)
        self.chart.legend().setVisible(True)
        self.chart.legend().setAlignment(Qt.AlignmentFlag.AlignBottom)
        self.chart.legend().setFont(QFont("Arial", 10))
        self.chart.legend().setLabelColor(QColor("#E6E8EA"))
        self.chart.layout().setContentsMargins(10, 10, 10, 10)
        
        # 创建K线系列
        self.candlestick_series = QCandlestickSeries()
        self.candlestick_series.setIncreasingColor(QColor("#02C076"))  # OKX绿色(涨)
        self.candlestick_series.setDecreasingColor(QColor("#F6465D"))  # OKX红色(跌)
        self.candlestick_series.setPen(QPen(QColor(0, 0, 0, 0)))  # 无边框
        self.candlestick_series.setBodyWidth(0.8)  # 设置K线宽度比例
        self.chart.addSeries(self.candlestick_series)
        
        # 创建技术指标系列
        self.indicator_series: Dict[str, QLineSeries] = {}
        
        # 创建坐标轴
        self.time_axis = QDateTimeAxis()
        self.time_axis.setFormat("MM-dd HH:mm")
        self.time_axis.setTitleText("时间")
        self.time_axis.setTitleFont(QFont("Arial", 10))
        self.time_axis.setLabelsFont(QFont("Arial", 9))
        self.time_axis.setGridLineVisible(True)
        self.time_axis.setGridLineColor(QColor("#1E2126"))
        self.time_axis.setLabelsColor(QColor("#848E9C"))
        self.time_axis.setTitleVisible(False)
        
        self.price_axis = QValueAxis()
        self.price_axis.setTitleText("价格")
        self.price_axis.setTitleFont(QFont("Arial", 10))
        self.price_axis.setLabelsFont(QFont("Arial", 9))
        self.price_axis.setGridLineVisible(True)
        self.price_axis.setGridLineColor(QColor("#1E2126"))
        self.price_axis.setLabelsColor(QColor("#848E9C"))
        self.price_axis.setTitleVisible(False)
        
        self.chart.addAxis(self.time_axis, Qt.AlignmentFlag.AlignBottom)
        self.chart.addAxis(self.price_axis, Qt.AlignmentFlag.AlignRight)
        
        self.candlestick_series.attachAxis(self.time_axis)
        self.candlestick_series.attachAxis(self.price_axis)
        
        # 创建图表视图
        chart_view = QChartView(self.chart)
        chart_view.setRenderHint(QPainter.RenderHint.Antialiasing)
        chart_view.setStyleSheet("""
            background-color: #0B0E11;
            border: none;
        """)
        chart_layout.addWidget(chart_view)
        
        # 将图表面板添加到主布局
        layout.addWidget(chart_panel)
        
    def update_data(self, ohlcv_data: List[List[Union[int, float]]]) -> None:
        """
        更新K线数据
        
        Args:
            ohlcv_data: OHLCV数据列表，每项包含[timestamp, open, high, low, close, volume]
        """
        try:
            if not ohlcv_data or len(ohlcv_data) < 2:
                print("错误: 无效的OHLCV数据")
                return
            
            # 清除现有数据
            self.candlestick_series.clear()
            for series in self.indicator_series.values():
                self.chart.removeSeries(series)
            self.indicator_series.clear()
            
            # 转换为pandas DataFrame
            df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # 数据过滤和清理
            # 确保所有价格数据为浮点数
            for col in ['open', 'high', 'low', 'close']:
                df[col] = df[col].astype(float)
            
            # 确保时间戳格式正确
            if df['timestamp'].iloc[0] > 1e12:  # 如果是毫秒时间戳
                df['timestamp'] = df['timestamp'].astype(float)
            else:  # 如果是秒时间戳
                df['timestamp'] = df['timestamp'].astype(float) * 1000
            
            # 降序排列
            df = df.sort_values('timestamp')
            
            # 更新K线图
            for index, row in df.iterrows():
                timestamp = QDateTime.fromMSecsSinceEpoch(int(row['timestamp']))
                candlestick_set = QCandlestickSet(
                    float(row['open']),
                    float(row['high']),
                    float(row['low']),
                    float(row['close']),
                    timestamp.toMSecsSinceEpoch()
                )
                self.candlestick_series.append(candlestick_set)
            
            # 计算ADX指标
            high_prices = df['high'].values
            low_prices = df['low'].values
            close_prices = df['close'].values
            adx = talib.ADX(high_prices, low_prices, close_prices, timeperiod=14)
            
            # 存储最新的ADX值
            if len(adx) > 0 and not np.isnan(adx[-1]):
                self.current_adx = round(float(adx[-1]), 2)
                self.adx_label.setText(f"ADX: {self.current_adx}")
                
                # 根据ADX值设置颜色
                if self.current_adx >= 25:
                    self.adx_label.setStyleSheet("color: #2EBD85; font-size: 13px; font-weight: bold;")  # 绿色
                else:
                    self.adx_label.setStyleSheet("color: #F6465D; font-size: 13px; font-weight: bold;")  # 红色
            else:
                self.current_adx = None
                self.adx_label.setText("ADX: N/A")
                self.adx_label.setStyleSheet("color: #848E9C; font-size: 13px;")
            
            # 更新技术指标
            self.update_indicator(self.indicator_combo.currentText(), df)
            
            # 调整坐标轴范围
            if len(df) > 0:
                # 设置时间轴范围
                min_timestamp = QDateTime.fromMSecsSinceEpoch(int(df['timestamp'].iloc[0]))
                max_timestamp = QDateTime.fromMSecsSinceEpoch(int(df['timestamp'].iloc[-1]))
                self.time_axis.setRange(min_timestamp, max_timestamp)
                
                # 设置价格轴范围 - 给上下留出一些空间
                min_price = df['low'].min() * 0.998
                max_price = df['high'].max() * 1.002
                self.price_axis.setRange(min_price, max_price)
                
                # 根据时间周期调整时间轴格式
                timeframe = self.timeframe_combo.currentText()
                if timeframe in ['5分钟', '15分钟']:
                    self.time_axis.setFormat("MM-dd HH:mm")
                elif timeframe in ['1小时', '4小时']:
                    self.time_axis.setFormat("MM-dd HH:mm")
                else:  # 日线及更长周期
                    self.time_axis.setFormat("yyyy-MM-dd")
                
                # 根据数据量调整标签数量
                if len(df) <= 30:
                    self.time_axis.setTickCount(5)
                elif len(df) <= 60:
                    self.time_axis.setTickCount(7)
                else:
                    self.time_axis.setTickCount(10)
            
        except Exception as e:
            print(f"更新图表数据时出错: {str(e)}")
            import traceback
            traceback.print_exc()
        
    def update_indicator(self, indicator_name: str, df: Optional[pd.DataFrame] = None) -> None:
        """
        更新技术指标
        
        Args:
            indicator_name: 技术指标名称（MA, MACD, RSI, BOLL, ADX）
            df: 数据框架，包含OHLCV数据
        """
        if df is None or len(df) == 0:
            return
            
        # 清除旧的指标
        for series in self.indicator_series.values():
            self.chart.removeSeries(series)
        self.indicator_series.clear()
        
        close_prices = df['close'].values
        timestamps = df['timestamp'].values.astype(float)  # 确保时间戳是浮点数
        
        if indicator_name == 'MA':
            # 添加MA5和MA10
            ma5 = talib.MA(close_prices, timeperiod=5)
            ma10 = talib.MA(close_prices, timeperiod=10)
            ma30 = talib.MA(close_prices, timeperiod=30)
            
            ma5_series = QLineSeries()
            ma5_series.setName("MA5")
            ma5_series.setPen(QPen(QColor("#F0B90B"), 1.5))
            
            ma10_series = QLineSeries()
            ma10_series.setName("MA10")
            ma10_series.setPen(QPen(QColor("#5973FE"), 1.5))
            
            ma30_series = QLineSeries()
            ma30_series.setName("MA30")
            ma30_series.setPen(QPen(QColor("#B93AF8"), 1.5))
            
            for i in range(len(timestamps)):
                if not np.isnan(ma5[i]):
                    ma5_series.append(QPointF(timestamps[i], ma5[i]))
                if not np.isnan(ma10[i]):
                    ma10_series.append(QPointF(timestamps[i], ma10[i]))
                if not np.isnan(ma30[i]):
                    ma30_series.append(QPointF(timestamps[i], ma30[i]))
                    
            self.chart.addSeries(ma5_series)
            self.chart.addSeries(ma10_series)
            self.chart.addSeries(ma30_series)
            ma5_series.attachAxis(self.time_axis)
            ma5_series.attachAxis(self.price_axis)
            ma10_series.attachAxis(self.time_axis)
            ma10_series.attachAxis(self.price_axis)
            ma30_series.attachAxis(self.time_axis)
            ma30_series.attachAxis(self.price_axis)
            
            self.indicator_series['MA5'] = ma5_series
            self.indicator_series['MA10'] = ma10_series
            self.indicator_series['MA30'] = ma30_series
            
        elif indicator_name == 'MACD':
            # 计算MACD
            macd, signal, hist = talib.MACD(close_prices)
            
            macd_series = QLineSeries()
            macd_series.setName("MACD")
            macd_series.setPen(QPen(QColor("#F0B90B"), 1.5))
            
            signal_series = QLineSeries()
            signal_series.setName("Signal")
            signal_series.setPen(QPen(QColor("#5973FE"), 1.5))
            
            for i in range(len(timestamps)):
                if not np.isnan(macd[i]):
                    macd_series.append(QPointF(timestamps[i], macd[i]))
                if not np.isnan(signal[i]):
                    signal_series.append(QPointF(timestamps[i], signal[i]))
                    
            self.chart.addSeries(macd_series)
            self.chart.addSeries(signal_series)
            macd_series.attachAxis(self.time_axis)
            macd_series.attachAxis(self.price_axis)
            signal_series.attachAxis(self.time_axis)
            signal_series.attachAxis(self.price_axis)
            
            self.indicator_series['MACD'] = macd_series
            self.indicator_series['Signal'] = signal_series
            
        elif indicator_name == 'RSI':
            # 计算RSI
            rsi = talib.RSI(close_prices)
            
            rsi_series = QLineSeries()
            rsi_series.setName("RSI")
            rsi_series.setPen(QPen(QColor("#F0B90B"), 1.5))
            
            # 添加超买超卖线
            overbought_series = QLineSeries()
            overbought_series.setName("超买")
            overbought_series.setPen(QPen(QColor("#F6465D"), 1.0, Qt.PenStyle.DashLine))
            
            oversold_series = QLineSeries()
            oversold_series.setName("超卖")
            oversold_series.setPen(QPen(QColor("#02C076"), 1.0, Qt.PenStyle.DashLine))
            
            for i in range(len(timestamps)):
                if not np.isnan(rsi[i]):
                    rsi_series.append(QPointF(timestamps[i], rsi[i]))
                
                # 添加超买线(70)和超卖线(30)
                overbought_series.append(QPointF(timestamps[i], 70))
                oversold_series.append(QPointF(timestamps[i], 30))
                    
            self.chart.addSeries(rsi_series)
            self.chart.addSeries(overbought_series)
            self.chart.addSeries(oversold_series)
            
            rsi_series.attachAxis(self.time_axis)
            rsi_series.attachAxis(self.price_axis)
            overbought_series.attachAxis(self.time_axis)
            overbought_series.attachAxis(self.price_axis)
            oversold_series.attachAxis(self.time_axis)
            oversold_series.attachAxis(self.price_axis)
            
            self.indicator_series['RSI'] = rsi_series
            self.indicator_series['Overbought'] = overbought_series
            self.indicator_series['Oversold'] = oversold_series
            
        elif indicator_name == 'BOLL':
            # 计算布林带
            upper, middle, lower = talib.BBANDS(close_prices)
            
            upper_series = QLineSeries()
            upper_series.setName("上轨")
            upper_series.setPen(QPen(QColor("#F6465D"), 1.5))
            
            middle_series = QLineSeries()
            middle_series.setName("中轨")
            middle_series.setPen(QPen(QColor("#F0B90B"), 1.5))
            
            lower_series = QLineSeries()
            lower_series.setName("下轨")
            lower_series.setPen(QPen(QColor("#02C076"), 1.5))
            
            for i in range(len(timestamps)):
                if not np.isnan(upper[i]):
                    upper_series.append(QPointF(timestamps[i], upper[i]))
                if not np.isnan(middle[i]):
                    middle_series.append(QPointF(timestamps[i], middle[i]))
                if not np.isnan(lower[i]):
                    lower_series.append(QPointF(timestamps[i], lower[i]))
                    
            self.chart.addSeries(upper_series)
            self.chart.addSeries(middle_series)
            self.chart.addSeries(lower_series)
            
            upper_series.attachAxis(self.time_axis)
            upper_series.attachAxis(self.price_axis)
            middle_series.attachAxis(self.time_axis)
            middle_series.attachAxis(self.price_axis)
            lower_series.attachAxis(self.time_axis)
            lower_series.attachAxis(self.price_axis)
            
            self.indicator_series['Upper'] = upper_series
            self.indicator_series['Middle'] = middle_series
            self.indicator_series['Lower'] = lower_series
            
        elif indicator_name == 'ADX':
            # 计算ADX指标
            high_prices = df['high'].values
            low_prices = df['low'].values
            adx = talib.ADX(high_prices, low_prices, close_prices, timeperiod=14)
            plus_di = talib.PLUS_DI(high_prices, low_prices, close_prices, timeperiod=14)
            minus_di = talib.MINUS_DI(high_prices, low_prices, close_prices, timeperiod=14)
            
            # 创建ADX线
            adx_series = QLineSeries()
            adx_series.setName("ADX")
            adx_series.setPen(QPen(QColor("#F0B90B"), 1.5))
            
            # 创建+DI线
            plus_di_series = QLineSeries()
            plus_di_series.setName("+DI")
            plus_di_series.setPen(QPen(QColor("#2EBD85"), 1.5))
            
            # 创建-DI线
            minus_di_series = QLineSeries()
            minus_di_series.setName("-DI")
            minus_di_series.setPen(QPen(QColor("#F6465D"), 1.5))
            
            # 创建ADX阈值线（25）
            threshold_series = QLineSeries()
            threshold_series.setName("阈值 (25)")
            threshold_series.setPen(QPen(QColor("#848E9C"), 1.0, Qt.PenStyle.DashLine))
            
            for i in range(len(timestamps)):
                if not np.isnan(adx[i]):
                    adx_series.append(QPointF(timestamps[i], adx[i]))
                if not np.isnan(plus_di[i]):
                    plus_di_series.append(QPointF(timestamps[i], plus_di[i]))
                if not np.isnan(minus_di[i]):
                    minus_di_series.append(QPointF(timestamps[i], minus_di[i]))
                
                # 添加阈值线
                threshold_series.append(QPointF(timestamps[i], 25))
            
            # 添加到图表
            self.chart.addSeries(adx_series)
            self.chart.addSeries(plus_di_series)
            self.chart.addSeries(minus_di_series)
            self.chart.addSeries(threshold_series)
            
            # 连接到坐标轴
            adx_series.attachAxis(self.time_axis)
            adx_series.attachAxis(self.price_axis)
            plus_di_series.attachAxis(self.time_axis)
            plus_di_series.attachAxis(self.price_axis)
            minus_di_series.attachAxis(self.time_axis)
            minus_di_series.attachAxis(self.price_axis)
            threshold_series.attachAxis(self.time_axis)
            threshold_series.attachAxis(self.price_axis)
            
            # 保存引用
            self.indicator_series['ADX'] = adx_series
            self.indicator_series['+DI'] = plus_di_series
            self.indicator_series['-DI'] = minus_di_series
            self.indicator_series['Threshold'] = threshold_series
            
            # 调整价格轴范围以适应ADX指标
            self.price_axis.setRange(0, 100)
            
    def on_timeframe_changed(self, timeframe: str) -> None:
        """
        处理时间周期变化
        
        Args:
            timeframe: 新的时间周期
        """
        try:
            # 通知主窗口刷新数据
            self.timeframe_changed.emit()
        except Exception as e:
            print(f"时间周期变化处理失败: {str(e)}") 