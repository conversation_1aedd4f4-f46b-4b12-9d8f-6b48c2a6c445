#!/usr/bin/env python3
"""
最终系统测试脚本
验证所有修复是否正常工作
"""

import os
import sys
import time
import json
import requests
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter
from dotenv import load_dotenv
from datetime import datetime

def test_environment():
    """测试环境配置"""
    print("🔧 环境配置测试")
    print("-" * 40)
    
    # 检查.env文件
    if os.path.exists('.env'):
        print("✅ .env文件存在")
        load_dotenv()
        
        # 检查必要的API密钥
        api_keys = {
            'OKX_API_KEY': os.getenv('OKX_API_KEY'),
            'OKX_SECRET_KEY': os.getenv('OKX_SECRET_KEY'),
            'OKX_PASSPHRASE': os.getenv('OKX_PASSPHRASE'),
            'NEWS_API_KEY': os.getenv('NEWS_API_KEY'),
            'DEEPSEEK_API_KEY': os.getenv('DEEPSEEK_API_KEY')
        }
        
        for key, value in api_keys.items():
            if value:
                print(f"✅ {key}: {value[:10]}...")
            else:
                print(f"❌ {key}: 未设置")
    else:
        print("❌ .env文件不存在")
        return False
    
    return True

def test_config_files():
    """测试配置文件"""
    print("\n📁 配置文件测试")
    print("-" * 40)
    
    config_files = ['config.json', 'trading_settings.json']
    all_good = True
    
    for file_name in config_files:
        if os.path.exists(file_name):
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✅ {file_name}: 格式正确")
                
                # 检查止盈止损设置
                if 'tp_percent' in config and 'sl_percent' in config:
                    print(f"   止盈: {config['tp_percent']}%, 止损: {config['sl_percent']}%")
                
            except Exception as e:
                print(f"❌ {file_name}: 读取失败 - {str(e)}")
                all_good = False
        else:
            print(f"⚠️  {file_name}: 不存在")
    
    return all_good

def make_robust_request(url, params=None, headers=None, method='GET', json_data=None, max_retries=3, timeout=(15, 45)):
    """强大的网络请求方法"""
    for attempt in range(max_retries):
        try:
            session = requests.Session()
            
            retry_strategy = Retry(
                total=3,
                backoff_factor=2,
                status_forcelist=[429, 500, 502, 503, 504, 520, 521, 522, 523, 524],
                allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
            )
            
            adapter = HTTPAdapter(
                max_retries=retry_strategy,
                pool_connections=10,
                pool_maxsize=20
            )
            
            session.mount("http://", adapter)
            session.mount("https://", adapter)
            
            if headers is None:
                headers = {}
            headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if method.upper() == 'GET':
                response = session.get(url, params=params, headers=headers, timeout=timeout, verify=True)
            elif method.upper() == 'POST':
                response = session.post(url, params=params, headers=headers, json=json_data, timeout=timeout, verify=True)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            response.raise_for_status()
            return response
            
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + 1
                print(f"   重试 {attempt + 1}/{max_retries} (等待{wait_time}秒): {str(e)}")
                time.sleep(wait_time)
            else:
                raise
    
    raise Exception(f"在 {max_retries} 次尝试后仍然失败")

def test_news_api():
    """测试News API连接"""
    print("\n📰 News API连接测试")
    print("-" * 40)
    
    api_key = os.getenv('NEWS_API_KEY')
    if not api_key:
        print("❌ NEWS_API_KEY未设置")
        return False
    
    try:
        url = 'https://newsapi.org/v2/everything'
        params = {
            'q': 'bitcoin OR cryptocurrency',
            'pageSize': 3,
            'language': 'en',
            'sortBy': 'publishedAt',
            'apiKey': api_key
        }
        
        print("🔄 发送请求...")
        response = make_robust_request(url=url, params=params, timeout=(15, 45))
        
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'ok':
                articles = data.get('articles', [])
                print(f"✅ 成功获取 {len(articles)} 条新闻")
                
                for i, article in enumerate(articles, 1):
                    title = article.get('title', 'No title')
                    print(f"   {i}. {title[:60]}...")
                
                return True
            else:
                print(f"❌ API返回错误: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return False

def test_deepseek_api():
    """测试DeepSeek API连接"""
    print("\n🤖 DeepSeek API连接测试")
    print("-" * 40)
    
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if not api_key:
        print("❌ DEEPSEEK_API_KEY未设置")
        return False
    
    try:
        url = "https://api.deepseek.com/v1/chat/completions"
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        data = {
            "model": "deepseek-chat",
            "messages": [{"role": "user", "content": "Hello, test connection"}],
            "max_tokens": 10
        }
        
        print("🔄 发送测试请求...")
        response = make_robust_request(
            url=url, 
            headers=headers, 
            json_data=data, 
            method='POST',
            timeout=(15, 45)
        )
        
        if response.status_code == 200:
            print("✅ DeepSeek API连接成功")
            return True
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return False

def test_main_window_imports():
    """测试主窗口模块导入"""
    print("\n🐍 Python模块导入测试")
    print("-" * 40)
    
    try:
        # 测试关键模块导入
        import requests
        print("✅ requests模块")
        
        from urllib3.util.retry import Retry
        print("✅ urllib3.util.retry模块")
        
        from requests.adapters import HTTPAdapter
        print("✅ requests.adapters模块")
        
        import pandas as pd
        print("✅ pandas模块")
        
        # 检查main_window.py文件
        if os.path.exists('main_window.py'):
            print("✅ main_window.py文件存在")
            
            # 检查关键函数
            with open('main_window.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            key_functions = [
                'make_robust_request',
                'apply_tp_sl_settings',
                'test_news_api',
                'get_trading_signal'
            ]
            
            for func in key_functions:
                if f'def {func}' in content:
                    print(f"✅ {func}函数存在")
                else:
                    print(f"❌ {func}函数缺失")
                    return False
        else:
            print("❌ main_window.py文件不存在")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {str(e)}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "="*60)
    print("📊 最终测试报告")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！系统已完全修复并正常工作。")
        print("\n✨ 系统状态: 🟢 正常运行")
        print("📋 可以安全使用以下功能:")
        print("   • News API新闻获取")
        print("   • AI分析和交易信号")
        print("   • 止盈止损设置")
        print("   • 自动化交易")
    else:
        print("\n⚠️  部分测试失败，请检查失败的项目。")
        print("📋 建议:")
        print("   • 检查API密钥配置")
        print("   • 验证网络连接")
        print("   • 查看错误日志")

def main():
    """主测试函数"""
    print("🚀 OKX量化交易机器人 - 最终系统测试")
    print("="*60)
    
    # 执行所有测试
    test_results = {}
    
    test_results['环境配置'] = test_environment()
    test_results['配置文件'] = test_config_files()
    test_results['模块导入'] = test_main_window_imports()
    test_results['News API'] = test_news_api()
    test_results['DeepSeek API'] = test_deepseek_api()
    
    # 生成报告
    generate_test_report(test_results)

if __name__ == "__main__":
    main()
