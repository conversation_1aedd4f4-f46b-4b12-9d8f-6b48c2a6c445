#!/usr/bin/env python3
"""
ADX功能检查脚本
用于诊断ADX（平均趋向指数）功能是否正常工作
"""

import os
import json
import re
import ccxt
import talib
import numpy as np
from datetime import datetime
from dotenv import load_dotenv

def check_adx_dependencies():
    """检查ADX计算所需的依赖"""
    print("=== ADX依赖检查 ===")
    
    dependencies = {
        'talib': 'TA-Lib技术分析库',
        'numpy': 'NumPy数值计算库',
        'ccxt': '加密货币交易所连接库'
    }
    
    all_ok = True
    for module, description in dependencies.items():
        try:
            __import__(module)
            print(f"✓ {module}: {description}")
        except ImportError:
            print(f"✗ {module}: 缺失 - {description}")
            all_ok = False
    
    return all_ok

def check_adx_configuration():
    """检查ADX配置"""
    print("\n=== ADX配置检查 ===")
    
    config_files = ['indicator_settings.json', 'trading_settings.json']
    adx_configs = {}
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                print(f"\n{config_file}:")
                adx_period = config.get('adx_period', None)
                adx_threshold = config.get('adx_threshold', None)
                
                if adx_period:
                    print(f"  ✓ ADX周期: {adx_period}")
                    adx_configs['period'] = adx_period
                else:
                    print(f"  ✗ ADX周期: 未设置")
                
                if adx_threshold:
                    print(f"  ✓ ADX阈值: {adx_threshold}")
                    adx_configs['threshold'] = adx_threshold
                else:
                    print(f"  ✗ ADX阈值: 未设置")
                    
            except Exception as e:
                print(f"  ✗ {config_file} 读取失败: {str(e)}")
        else:
            print(f"  ✗ {config_file} 不存在")
    
    return adx_configs

def check_adx_code_implementation():
    """检查ADX代码实现"""
    print("\n=== ADX代码实现检查 ===")
    
    if not os.path.exists('main_window.py'):
        print("✗ main_window.py文件不存在")
        return False
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键ADX功能
    checks = [
        {
            'name': 'ADX变量初始化',
            'pattern': 'self.current_adx = None',
            'description': 'ADX当前值变量'
        },
        {
            'name': 'ADX更新定时器',
            'pattern': 'self.adx_update_timer',
            'description': 'ADX定时更新机制'
        },
        {
            'name': 'ADX计算函数',
            'pattern': 'def calculate_adx_optimized',
            'description': 'ADX计算方法'
        },
        {
            'name': 'ADX显示更新',
            'pattern': 'def update_adx_display',
            'description': 'ADX显示更新函数'
        },
        {
            'name': 'ADX UI更新',
            'pattern': 'def update_adx_ui',
            'description': 'ADX用户界面更新'
        },
        {
            'name': 'ADX信号检查',
            'pattern': 'def check_adx_signals',
            'description': 'ADX交易信号检查'
        },
        {
            'name': 'TA-Lib ADX调用',
            'pattern': 'talib.ADX',
            'description': 'TA-Lib ADX计算调用'
        },
        {
            'name': 'ADX UI控件',
            'pattern': 'self.adx_value_display',
            'description': 'ADX值显示控件'
        }
    ]
    
    all_passed = True
    for check in checks:
        if check['pattern'] in content:
            print(f"✓ {check['name']}: {check['description']}")
        else:
            print(f"✗ {check['name']}: 缺失 - {check['description']}")
            all_passed = False
    
    return all_passed

def test_adx_calculation():
    """测试ADX计算功能"""
    print("\n=== ADX计算测试 ===")
    
    try:
        # 生成模拟价格数据
        np.random.seed(42)  # 固定随机种子以获得可重复的结果
        
        # 模拟50个价格点
        base_price = 50000
        price_changes = np.random.normal(0, 0.02, 50)  # 2%的标准差
        prices = [base_price]
        
        for change in price_changes:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        # 生成OHLC数据
        high_prices = np.array([p * (1 + abs(np.random.normal(0, 0.01))) for p in prices])
        low_prices = np.array([p * (1 - abs(np.random.normal(0, 0.01))) for p in prices])
        close_prices = np.array(prices)
        
        print(f"生成了{len(close_prices)}个价格点")
        print(f"价格范围: ${low_prices.min():.2f} - ${high_prices.max():.2f}")
        
        # 计算ADX
        adx_period = 14
        adx = talib.ADX(high_prices, low_prices, close_prices, timeperiod=adx_period)
        plus_di = talib.PLUS_DI(high_prices, low_prices, close_prices, timeperiod=adx_period)
        minus_di = talib.MINUS_DI(high_prices, low_prices, close_prices, timeperiod=adx_period)
        
        # 检查计算结果
        if len(adx) > 0 and not np.isnan(adx[-1]):
            current_adx = round(float(adx[-1]), 2)
            current_plus_di = round(float(plus_di[-1]), 2)
            current_minus_di = round(float(minus_di[-1]), 2)
            
            print(f"✓ ADX计算成功")
            print(f"  当前ADX: {current_adx}")
            print(f"  +DI: {current_plus_di}")
            print(f"  -DI: {current_minus_di}")
            
            # 判断趋势
            if current_plus_di > current_minus_di:
                trend = "看多"
            elif current_minus_di > current_plus_di:
                trend = "看空"
            else:
                trend = "中性"
            
            # 判断强度
            if current_adx >= 40:
                strength = "极强"
            elif current_adx >= 25:
                strength = "强"
            else:
                strength = "弱"
            
            print(f"  趋势方向: {trend}")
            print(f"  趋势强度: {strength}")
            
            return True
        else:
            print("✗ ADX计算失败: 结果为NaN或空")
            return False
            
    except Exception as e:
        print(f"✗ ADX计算测试失败: {str(e)}")
        return False

def test_real_market_adx():
    """测试真实市场数据的ADX计算"""
    print("\n=== 真实市场ADX测试 ===")
    
    try:
        # 加载环境变量
        load_dotenv()
        api_key = os.getenv('OKX_API_KEY')
        secret_key = os.getenv('OKX_SECRET_KEY')
        passphrase = os.getenv('OKX_PASSPHRASE')

        if not api_key or not secret_key or not passphrase:
            print("✗ 缺少OKX API密钥，跳过真实市场测试")
            return False

        # 初始化交易所
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': False,
            'options': {
                'defaultType': 'swap',
                'adjustForTimeDifference': True,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'recvWindow': 10000
            }
        })
        
        # 获取BTC-USDT-SWAP的15分钟K线数据
        symbol = 'BTC-USDT-SWAP'
        timeframe = '15m'
        limit = 50
        
        print(f"获取{symbol} {timeframe}数据...")
        klines = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
        
        if not klines or len(klines) < 30:
            print(f"✗ K线数据不足: {len(klines) if klines else 0}条")
            return False
        
        print(f"✓ 获取到{len(klines)}条K线数据")
        
        # 提取价格数据
        high_prices = np.array([float(k[2]) for k in klines])
        low_prices = np.array([float(k[3]) for k in klines])
        close_prices = np.array([float(k[4]) for k in klines])
        
        # 计算ADX
        adx_period = 14
        adx = talib.ADX(high_prices, low_prices, close_prices, timeperiod=adx_period)
        plus_di = talib.PLUS_DI(high_prices, low_prices, close_prices, timeperiod=adx_period)
        minus_di = talib.MINUS_DI(high_prices, low_prices, close_prices, timeperiod=adx_period)
        
        # 显示结果
        if len(adx) > 0 and not np.isnan(adx[-1]):
            current_adx = round(float(adx[-1]), 2)
            current_plus_di = round(float(plus_di[-1]), 2)
            current_minus_di = round(float(minus_di[-1]), 2)
            current_price = close_prices[-1]
            
            print(f"✓ {symbol}实时ADX计算成功")
            print(f"  当前价格: ${current_price:.2f}")
            print(f"  ADX: {current_adx}")
            print(f"  +DI: {current_plus_di}")
            print(f"  -DI: {current_minus_di}")
            
            # 市场分析
            if current_plus_di > current_minus_di:
                trend = "上涨趋势"
                trend_strength = current_plus_di - current_minus_di
            else:
                trend = "下跌趋势"
                trend_strength = current_minus_di - current_plus_di
            
            if current_adx >= 40:
                strength = "极强趋势"
            elif current_adx >= 25:
                strength = "强趋势"
            else:
                strength = "弱趋势/震荡"
            
            print(f"  市场状态: {trend} ({strength})")
            print(f"  趋势强度差: {trend_strength:.2f}")
            
            return True
        else:
            print("✗ ADX计算失败")
            return False
            
    except Exception as e:
        print(f"✗ 真实市场ADX测试失败: {str(e)}")
        return False

def check_adx_ui_elements():
    """检查ADX UI元素"""
    print("\n=== ADX UI元素检查 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    ui_elements = [
        'self.adx_value_display',
        'self.plus_di_display', 
        'self.minus_di_display',
        'self.adx_trend_status_label',
        'self.adx_strength_label',
        'self.adx_threshold_label',
        'self.adx_last_update_label',
        'self.adx_progress_container',
        'self.adx_progress_fill'
    ]
    
    all_found = True
    for element in ui_elements:
        if element in content:
            print(f"✓ {element}: UI元素存在")
        else:
            print(f"✗ {element}: UI元素缺失")
            all_found = False
    
    return all_found

def monitor_adx_real_time():
    """实时监控ADX变化"""
    print("\n=== ADX实时监控 ===")
    print("监控BTC-USDT-SWAP的ADX变化，按Ctrl+C停止...")

    try:
        # 加载环境变量
        load_dotenv()
        api_key = os.getenv('OKX_API_KEY')
        secret_key = os.getenv('OKX_SECRET_KEY')
        passphrase = os.getenv('OKX_PASSPHRASE')

        if not api_key or not secret_key or not passphrase:
            print("✗ 缺少OKX API密钥，无法进行实时监控")
            return

        # 初始化交易所
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'sandbox': False,
            'options': {
                'defaultType': 'swap',
                'adjustForTimeDifference': True,
                'warnOnFetchOpenOrdersWithoutSymbol': False,
                'recvWindow': 10000
            }
        })

        symbol = 'BTC-USDT-SWAP'
        timeframe = '15m'
        adx_period = 14
        adx_threshold = 25
        adx_strong_threshold = 40

        print(f"监控参数: 周期={adx_period}, 阈值={adx_threshold}, 强阈值={adx_strong_threshold}")
        print("时间\t\t价格\t\tADX\t+DI\t-DI\t趋势状态")
        print("-" * 80)

        last_adx = None
        last_plus_di = None
        last_minus_di = None

        while True:
            try:
                # 获取K线数据
                klines = exchange.fetch_ohlcv(symbol, timeframe, limit=50)

                if not klines or len(klines) < 30:
                    print("数据不足，等待...")
                    time.sleep(30)
                    continue

                # 提取价格数据
                high_prices = np.array([float(k[2]) for k in klines])
                low_prices = np.array([float(k[3]) for k in klines])
                close_prices = np.array([float(k[4]) for k in klines])
                current_price = close_prices[-1]

                # 计算ADX
                adx = talib.ADX(high_prices, low_prices, close_prices, timeperiod=adx_period)
                plus_di = talib.PLUS_DI(high_prices, low_prices, close_prices, timeperiod=adx_period)
                minus_di = talib.MINUS_DI(high_prices, low_prices, close_prices, timeperiod=adx_period)

                if len(adx) > 0 and not np.isnan(adx[-1]):
                    current_adx = round(float(adx[-1]), 2)
                    current_plus_di = round(float(plus_di[-1]), 2)
                    current_minus_di = round(float(minus_di[-1]), 2)
                    current_time = datetime.now().strftime("%H:%M:%S")

                    # 判断趋势状态
                    if current_adx >= adx_strong_threshold:
                        if current_plus_di > current_minus_di:
                            trend_status = "🚀 极强上涨"
                        else:
                            trend_status = "📉 极强下跌"
                    elif current_adx >= adx_threshold:
                        if current_plus_di > current_minus_di:
                            trend_status = "⬆️ 强势上涨"
                        else:
                            trend_status = "⬇️ 强势下跌"
                    else:
                        trend_status = "📊 震荡/弱势"

                    # 检测变化
                    change_indicator = ""
                    if last_adx is not None:
                        adx_change = current_adx - last_adx
                        if abs(adx_change) >= 1.0:
                            change_indicator = f"({adx_change:+.1f})"

                    print(f"{current_time}\t${current_price:.0f}\t\t{current_adx:.1f}{change_indicator}\t{current_plus_di:.1f}\t{current_minus_di:.1f}\t{trend_status}")

                    # 检测重要信号
                    if last_adx is not None:
                        # ADX突破阈值
                        if last_adx < adx_threshold and current_adx >= adx_threshold:
                            print(f"  🔔 ADX突破{adx_threshold}，趋势开始！")
                        elif last_adx >= adx_threshold and current_adx < adx_threshold:
                            print(f"  🔔 ADX跌破{adx_threshold}，趋势结束！")

                        # ADX突破强阈值
                        if last_adx < adx_strong_threshold and current_adx >= adx_strong_threshold:
                            print(f"  ⚡ ADX突破{adx_strong_threshold}，极强趋势！")

                        # DI交叉
                        if (last_plus_di <= last_minus_di and current_plus_di > current_minus_di):
                            print(f"  🔄 +DI上穿-DI，看多信号！")
                        elif (last_plus_di >= last_minus_di and current_plus_di < current_minus_di):
                            print(f"  🔄 -DI上穿+DI，看空信号！")

                    # 更新历史值
                    last_adx = current_adx
                    last_plus_di = current_plus_di
                    last_minus_di = current_minus_di

                time.sleep(30)  # 每30秒更新一次

            except KeyboardInterrupt:
                print("\n监控已停止")
                break
            except Exception as e:
                print(f"监控错误: {str(e)}")
                time.sleep(60)

    except Exception as e:
        print(f"监控初始化失败: {str(e)}")

def main():
    """主函数"""
    print("ADX功能检查")
    print("=" * 50)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 执行各项检查
    deps_ok = check_adx_dependencies()
    config_ok = check_adx_configuration()
    code_ok = check_adx_code_implementation()
    calc_ok = test_adx_calculation()
    market_ok = test_real_market_adx()
    ui_ok = check_adx_ui_elements()

    # 总结
    print("\n=== 检查总结 ===")
    print(f"依赖库: {'✓' if deps_ok else '✗'}")
    print(f"配置文件: {'✓' if config_ok else '✗'}")
    print(f"代码实现: {'✓' if code_ok else '✗'}")
    print(f"计算测试: {'✓' if calc_ok else '✗'}")
    print(f"市场数据: {'✓' if market_ok else '✗'}")
    print(f"UI元素: {'✓' if ui_ok else '✗'}")

    all_ok = deps_ok and code_ok and calc_ok and ui_ok

    if all_ok:
        print("\n🎉 ADX功能正常工作！")
        print("\nADX功能特性:")
        print("- 实时计算ADX、+DI、-DI指标")
        print("- 自动判断趋势方向和强度")
        print("- 可视化进度条和状态显示")
        print("- 智能缓存机制提高性能")
        print("- 交易信号检测和通知")

        # 询问是否进行实时监控
        print("\n是否要进行ADX实时监控？(y/n): ", end="")
        try:
            choice = input().lower().strip()
            if choice == 'y' or choice == 'yes':
                monitor_adx_real_time()
        except KeyboardInterrupt:
            print("\n程序已退出")
    else:
        print("\n⚠️  ADX功能存在问题")

        if not deps_ok:
            print("- 请安装缺失的依赖库")
        if not code_ok:
            print("- 检查代码实现是否完整")
        if not calc_ok:
            print("- ADX计算功能有问题")
        if not ui_ok:
            print("- UI元素不完整")

if __name__ == "__main__":
    main()
