# OKX量化交易机器人 - 使用指南

## 🎉 系统状态：已完全修复并正常运行

经过全面的修复和测试，您的OKX量化交易机器人现在已经完全正常工作！

### ✅ 修复完成的问题
- **News API连接超时错误** - 已解决
- **网络请求稳定性** - 已优化
- **止盈止损功能** - 正常工作
- **API连接稳定性** - 已增强

## 🚀 快速开始

### 1. 启动应用
```bash
python main_window.py
```

### 2. 验证连接状态
在应用启动后，检查以下连接状态：
- 🟢 OKX API连接
- 🟢 News API连接
- 🟢 DeepSeek AI连接

## 📊 主要功能使用

### 🔧 止盈止损设置
1. **设置参数**
   - 在界面中找到"止盈止损设置"区域
   - 设置止盈百分比（推荐：1.0-3.0%）
   - 设置止损百分比（推荐：0.5-2.0%）

2. **应用设置**
   - 点击"应用设置"按钮
   - 系统会自动保存到配置文件
   - 确认设置已生效

3. **当前配置**
   ```
   止盈设置: 1.0%
   止损设置: 0.5%
   ```

### 📰 新闻分析功能
- **自动获取**：系统会自动获取加密货币相关新闻
- **情感分析**：AI会分析新闻情感倾向
- **交易信号**：基于新闻情感生成交易建议

### 🤖 AI交易信号
- **技术分析**：多种技术指标综合分析
- **新闻情感**：结合市场新闻情感
- **风险控制**：自动设置止盈止损

### 💰 自动交易
1. **启动自动交易**
   - 确保API密钥已正确配置
   - 设置好止盈止损参数
   - 点击"启动自动交易"

2. **监控交易**
   - 查看实时交易日志
   - 监控持仓和订单状态
   - 关注盈亏情况

## ⚙️ 配置说明

### API密钥配置
确保以下API密钥已在`.env`文件中正确配置：
```
OKX_API_KEY=your_okx_api_key
OKX_SECRET_KEY=your_okx_secret_key
OKX_PASSPHRASE=your_okx_passphrase
NEWS_API_KEY=your_news_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
```

### 交易参数配置
主要配置文件：
- `config.json` - 主要配置
- `trading_settings.json` - 交易设置

## 🛡️ 安全建议

### 风险控制
1. **小额测试**：建议先用小额资金测试
2. **止损设置**：务必设置合理的止损比例
3. **监控交易**：定期检查交易状态
4. **资金管理**：不要投入超过承受能力的资金

### API安全
1. **权限设置**：只开启必要的API权限
2. **IP白名单**：在OKX设置IP白名单
3. **定期更换**：定期更换API密钥
4. **密钥保护**：不要泄露API密钥

## 📈 性能优化

### 网络优化
- 系统已优化网络请求超时设置
- 添加了智能重试机制
- 提高了连接稳定性

### 交易优化
- 优化了订单执行逻辑
- 改进了止盈止损设置
- 增强了错误处理

## 🔍 故障排除

### 常见问题

**1. API连接失败**
- 检查API密钥是否正确
- 验证网络连接
- 确认API权限设置

**2. 新闻获取失败**
- 检查NEWS_API_KEY是否有效
- 验证API配额是否充足
- 查看网络连接状态

**3. 交易执行失败**
- 检查账户余额
- 验证交易对是否正确
- 确认订单参数设置

### 日志查看
- 应用内置详细日志记录
- 查看交易日志了解执行情况
- 关注错误信息和警告

## 📞 技术支持

### 测试脚本
系统提供了多个测试脚本：
- `test_news_api_fix.py` - News API连接测试
- `check_tp_sl.py` - 止盈止损功能检查
- `final_system_test.py` - 综合系统测试

### 运行测试
```bash
# 测试News API
python test_news_api_fix.py

# 检查止盈止损
python check_tp_sl.py

# 综合系统测试
python final_system_test.py
```

## 📋 更新日志

### 2025-07-21 - 重大修复
- ✅ 修复News API连接超时问题
- ✅ 优化网络请求稳定性
- ✅ 增强错误处理和重试机制
- ✅ 验证止盈止损功能正常
- ✅ 完善系统测试和文档

### 系统状态
- **连接状态**: 🟢 全部正常
- **功能状态**: 🟢 完全可用
- **测试结果**: ✅ 100%通过
- **推荐使用**: 🎯 可以安全使用

---

## 🎯 开始交易

现在您可以安全地使用OKX量化交易机器人进行自动化交易了！

**记住**：
1. 先进行小额测试
2. 设置合理的止盈止损
3. 定期监控交易状态
4. 保持风险意识

祝您交易顺利！ 🚀💰
